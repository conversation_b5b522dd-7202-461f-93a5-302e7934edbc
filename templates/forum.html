<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区论坛 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px);
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            margin-bottom: 1rem;
        }

        .post-card {
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .post-card:hover {
            transform: translateY(-2px);
        }

        .category-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
        }

        .pinned-post {
            border-left: 4px solid #ffd700;
        }

        .locked-post {
            opacity: 0.7;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }

        .btn-outline-light {
            border-color: rgba(255, 255, 255, 0.5);
        }

        .search-box {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .stats-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffd700;
        }

        .stats-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .category-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">策略市场</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">回测系统</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/forum">社区论坛</a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-white">
                    <i class="fas fa-comments me-3"></i>社区论坛
                </h1>
                <p class="text-white-50">与量化交易爱好者交流经验，分享策略心得</p>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4" id="forumStats">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalPosts">-</div>
                    <div class="stats-label">总帖子数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalUsers">-</div>
                    <div class="stats-label">活跃用户</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalViews">-</div>
                    <div class="stats-label">总浏览量</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalLikes">-</div>
                    <div class="stats-label">总点赞数</div>
                </div>
            </div>
        </div>

        <!-- 分类导航 -->
        <div class="category-section">
            <div class="row">
                <div class="col-12">
                    <h5 class="text-white mb-3">讨论分类</h5>
                </div>
            </div>
            <div class="row">
                <div class="col-md-2">
                    <button class="btn btn-outline-light w-100 mb-2" onclick="filterByCategory('')">
                        <i class="fas fa-th-large me-1"></i>全部
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-light w-100 mb-2" onclick="filterByCategory('general')">
                        <i class="fas fa-comments me-1"></i>综合讨论
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-light w-100 mb-2" onclick="filterByCategory('strategy')">
                        <i class="fas fa-code me-1"></i>策略分享
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-light w-100 mb-2" onclick="filterByCategory('market')">
                        <i class="fas fa-chart-line me-1"></i>市场分析
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-light w-100 mb-2" onclick="filterByCategory('help')">
                        <i class="fas fa-question-circle me-1"></i>新手求助
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-light w-100 mb-2" onclick="filterByCategory('news')">
                        <i class="fas fa-newspaper me-1"></i>行业资讯
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：帖子列表 -->
            <div class="col-md-8">
                <!-- 搜索和操作栏 -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" class="form-control search-box" id="searchInput"
                                           placeholder="搜索帖子标题、内容...">
                                    <button class="btn btn-primary" onclick="searchPosts()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="showCreatePost()">
                                    <i class="fas fa-plus me-1"></i>发布新帖
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 帖子列表 -->
                <div id="postsContainer">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x text-white"></i>
                        <p class="text-white mt-2">加载帖子中...</p>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <nav>
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 右侧：侧边栏 -->
            <div class="col-md-4">
                <!-- 热门话题 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-fire me-2"></i>热门话题
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush" id="hotTopicsList">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="small mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 活跃用户 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>活跃用户
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item bg-transparent border-0 text-white">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2">A</div>
                                    <div>
                                        <div class="fw-bold">AlgoTrader</div>
                                        <small class="text-white-50">策略专家</small>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item bg-transparent border-0 text-white">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2">Q</div>
                                    <div>
                                        <div class="fw-bold">QuantMaster</div>
                                        <small class="text-white-50">量化大师</small>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item bg-transparent border-0 text-white">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2">T</div>
                                    <div>
                                        <div class="fw-bold">TechAnalyst</div>
                                        <small class="text-white-50">技术分析师</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最新公告 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bullhorn me-2"></i>最新公告
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item bg-transparent border-0 text-white">
                                <div class="fw-bold">系统升级通知</div>
                                <small class="text-white-50">2025-01-15</small>
                                <p class="small mt-1">平台将于本周末进行系统升级...</p>
                            </div>
                            <div class="list-group-item bg-transparent border-0 text-white">
                                <div class="fw-bold">新功能发布</div>
                                <small class="text-white-50">2025-01-10</small>
                                <p class="small mt-1">新增策略回测优化功能...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 发布新帖模态框 -->
    <div class="modal fade" id="createPostModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
                <div class="modal-header">
                    <h5 class="modal-title">发布新帖</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createPostForm">
                        <div class="mb-3">
                            <label class="form-label">帖子标题</label>
                            <input type="text" class="form-control" id="postTitle" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分类</label>
                            <select class="form-select" id="postCategory" required>
                                <option value="">选择分类</option>
                                <option value="general">综合讨论</option>
                                <option value="strategy">策略分享</option>
                                <option value="market">市场分析</option>
                                <option value="help">新手求助</option>
                                <option value="news">行业资讯</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">帖子内容</label>
                            <textarea class="form-control" id="postContent" rows="8" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">标签 (用逗号分隔)</label>
                            <input type="text" class="form-control" id="postTags"
                                   placeholder="例如: 量化策略, 机器学习, 风险管理">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createPost()">发布帖子</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户注册</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" onsubmit="event.preventDefault(); performRegister();">
                        <div class="mb-3">
                            <label class="form-label text-white">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">全名</label>
                            <input type="text" class="form-control" id="registerFullName"
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/user-auth.js"></script>
    <script>
        let currentPage = 1;
        let currentCategory = '';
        let posts = [];

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadPosts();
            loadForumStats();
            loadHotTopics();
        });

        // 加载论坛统计数据
        async function loadForumStats() {
            try {
                const response = await fetch('/api/forum/stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;
                    document.getElementById('totalPosts').textContent = stats.total_posts?.toLocaleString() || '-';
                    document.getElementById('totalUsers').textContent = stats.total_users?.toLocaleString() || '-';
                    document.getElementById('totalViews').textContent = stats.total_views?.toLocaleString() || '-';
                    document.getElementById('totalLikes').textContent = stats.total_likes?.toLocaleString() || '-';
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 加载热门话题
        async function loadHotTopics() {
            try {
                const response = await fetch('/api/forum/hot-topics');
                const result = await response.json();

                if (result.success) {
                    displayHotTopics(result.hot_topics);
                }
            } catch (error) {
                console.error('加载热门话题失败:', error);
            }
        }

        // 显示热门话题
        function displayHotTopics(topics) {
            const container = document.getElementById('hotTopicsList');

            container.innerHTML = topics.map(topic => {
                const trendIcon = topic.trend === 'up' ? 'fas fa-arrow-up text-success' :
                                topic.trend === 'down' ? 'fas fa-arrow-down text-danger' :
                                'fas fa-minus text-warning';

                return `
                    <div class="list-group-item bg-transparent border-0 text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>#${topic.tag}</span>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">${topic.count}</span>
                                <i class="${trendIcon} small"></i>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 加载帖子列表
        async function loadPosts(page = 1) {
            try {
                let url = `/api/forum/posts?page=${page}&per_page=10`;
                if (currentCategory) url += `&category=${currentCategory}`;

                const response = await fetch(url);
                const result = await response.json();

                if (result.success) {
                    posts = result.posts;
                    displayPosts(posts);
                    updatePagination(result.current_page, result.pages, result.total);
                } else {
                    showError('加载帖子失败');
                }
            } catch (error) {
                console.error('加载帖子失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示帖子列表
        function displayPosts(postsList) {
            const container = document.getElementById('postsContainer');

            if (postsList.length === 0) {
                container.innerHTML = `
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-comments fa-3x text-white-50 mb-3"></i>
                            <h4 class="text-white">暂无帖子</h4>
                            <p class="text-white-50">成为第一个发帖的用户吧！</p>
                            <button class="btn btn-primary" onclick="showCreatePost()">
                                <i class="fas fa-plus me-1"></i>发布新帖
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = postsList.map(post => {
                // 格式化时间
                const createdDate = new Date(post.created_at);
                const timeAgo = getTimeAgo(createdDate);

                // 处理标签
                const tagsHtml = post.tags && post.tags.length > 0 ?
                    post.tags.slice(0, 3).map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('') : '';

                return `
                <div class="card post-card ${post.is_pinned ? 'pinned-post' : ''} ${post.is_locked ? 'locked-post' : ''}"
                     onclick="viewPost(${post.id})">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    ${post.is_pinned ? '<i class="fas fa-thumbtack text-warning me-1"></i>' : ''}
                                    ${post.is_locked ? '<i class="fas fa-lock text-secondary me-1"></i>' : ''}
                                    ${post.is_featured ? '<i class="fas fa-star text-warning me-1"></i>' : ''}
                                    ${post.title}
                                </h6>
                                <p class="text-white-50 small mb-2">${post.content.substring(0, 150)}${post.content.length > 150 ? '...' : ''}</p>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">${post.author.charAt(0).toUpperCase()}</div>
                                <div>
                                    <small class="text-white">${post.author}</small>
                                    <br>
                                    <small class="text-white-50">${timeAgo}</small>
                                </div>
                            </div>

                            <div class="d-flex align-items-center">
                                <span class="category-badge me-2">${getCategoryName(post.category)}</span>
                                <div class="text-end">
                                    <div class="d-flex align-items-center text-white-50 small">
                                        <i class="fas fa-eye me-1"></i>${post.views.toLocaleString()}
                                        <i class="fas fa-heart ms-2 me-1"></i>${post.likes.toLocaleString()}
                                        <i class="fas fa-comments ms-2 me-1"></i>${post.reply_count}
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${tagsHtml ? `<div class="mt-2">${tagsHtml}</div>` : ''}
                    </div>
                </div>
                `;
            }).join('');
        }

        // 时间格式化函数
        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return '刚刚';
            } else if (diffInSeconds < 3600) {
                return `${Math.floor(diffInSeconds / 60)}分钟前`;
            } else if (diffInSeconds < 86400) {
                return `${Math.floor(diffInSeconds / 3600)}小时前`;
            } else if (diffInSeconds < 2592000) {
                return `${Math.floor(diffInSeconds / 86400)}天前`;
            } else {
                return date.toLocaleDateString();
            }
        }

        // 格式化日期函数
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 获取分类名称
        function getCategoryName(category) {
            const categories = {
                'general': '综合讨论',
                'strategy': '策略分享',
                'market': '市场分析',
                'help': '新手求助',
                'news': '行业资讯',
                'crypto': '数字货币',
                'risk': '风险管理',
                'data': '数据分析',
                'technology': '技术讨论',
                'education': '教育资源',
                'discussion': '讨论',
                'psychology': '交易心理',
                'arbitrage': '套利',
                'announcement': '公告',
                'resources': '资源',
                'feedback': '反馈',
                'regulation': '监管',
                'options': '期权',
                'futures': '期货',
                'forex': '外汇'
            };
            return categories[category] || category;
        }

        // 按分类筛选
        function filterByCategory(category) {
            currentCategory = category;
            currentPage = 1;
            loadPosts(1);
        }

        // 搜索帖子
        function searchPosts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const filteredPosts = posts.filter(post =>
                post.title.toLowerCase().includes(searchTerm) ||
                post.content.toLowerCase().includes(searchTerm)
            );
            displayPosts(filteredPosts);
        }

        // 显示发布新帖模态框
        function showCreatePost() {
            new bootstrap.Modal(document.getElementById('createPostModal')).show();
        }

        // 创建帖子
        async function createPost() {
            const formData = {
                title: document.getElementById('postTitle').value,
                content: document.getElementById('postContent').value,
                category: document.getElementById('postCategory').value,
                tags: document.getElementById('postTags').value.split(',').map(tag => tag.trim()).filter(tag => tag)
            };

            try {
                const response = await fetch('/api/forum/posts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('createPostModal')).hide();
                    showSuccess('帖子发布成功！');
                    loadPosts(); // 重新加载帖子列表
                } else {
                    showError(result.error || '发布帖子失败');
                }
            } catch (error) {
                console.error('发布帖子失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 查看帖子详情
        async function viewPost(postId) {
            try {
                const response = await fetch(`/api/forum/posts/${postId}`);
                const result = await response.json();

                if (result.success) {
                    showPostDetail(result.post);
                } else {
                    showError('加载帖子详情失败');
                }
            } catch (error) {
                console.error('加载帖子详情失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示帖子详情
        function showPostDetail(post) {
            const modalHtml = `
                <div class="modal fade" id="postDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    ${post.is_pinned ? '<i class="fas fa-thumbtack text-warning me-2"></i>' : ''}
                                    ${post.is_locked ? '<i class="fas fa-lock text-secondary me-2"></i>' : ''}
                                    ${post.title}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="badge bg-primary me-2">${getCategoryName(post.category)}</span>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>${post.author}
                                                <i class="fas fa-clock ms-3 me-1"></i>${formatDate(post.created_at)}
                                            </small>
                                        </div>
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>${post.views || 0}
                                                <i class="fas fa-heart ms-2 me-1"></i>${post.likes || 0}
                                                <i class="fas fa-comments ms-2 me-1"></i>${post.reply_count || 0}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="post-content">
                                    ${formatPostContent(post.content)}
                                </div>
                                ${post.tags && post.tags.length > 0 ? `
                                    <div class="mt-3">
                                        <h6>标签</h6>
                                        ${post.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-primary" onclick="likePost(${post.id})">
                                    <i class="fas fa-heart me-1"></i>点赞
                                </button>
                                <button type="button" class="btn btn-primary" onclick="replyToPost(${post.id})">
                                    <i class="fas fa-reply me-1"></i>回复
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('postDetailModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            new bootstrap.Modal(document.getElementById('postDetailModal')).show();
        }

        // 格式化帖子内容
        function formatPostContent(content) {
            // 将换行符转换为HTML换行
            let formatted = content.replace(/\n/g, '<br>');

            // 处理Markdown样式的粗体
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // 处理代码块
            formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>');

            // 处理行内代码
            formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

            return formatted;
        }

        // 获取分类名称
        function getCategoryName(category) {
            const categoryMap = {
                'general': '综合讨论',
                'strategy': '策略分享',
                'market': '市场分析',
                'help': '新手求助',
                'news': '行业资讯',
                'crypto': '数字货币',
                'risk': '风险管理',
                'risk_management': '风险管理',
                'machine_learning': '机器学习',
                'strategy_optimization': '策略优化'
            };
            return categoryMap[category] || category;
        }

        // 点赞帖子
        function likePost(postId) {
            showSuccess('点赞成功！');
        }

        // 回复帖子
        function replyToPost(postId) {
            showSuccess('回复功能开发中...');
        }

        // 更新分页
        function updatePagination(currentPage, totalPages, totalItems) {
            const pagination = document.getElementById('pagination');
            let paginationHTML = '';

            // 上一页
            if (currentPage > 1) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadPosts(${currentPage - 1})">上一页</a>
                    </li>
                `;
            }

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadPosts(${i})">${i}</a>
                    </li>
                `;
            }

            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadPosts(${currentPage + 1})">下一页</a>
                    </li>
                `;
            }

            pagination.innerHTML = paginationHTML;
        }

        // 显示成功消息
        function showSuccess(message) {
            showNotification(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showNotification(message, 'error');
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 显示登录模态框
        function showLogin() {
            new bootstrap.Modal(document.getElementById('loginModal')).show();
        }

        // 显示注册模态框
        function showRegister() {
            new bootstrap.Modal(document.getElementById('registerModal')).show();
        }

        // 登录表单提交
        async function submitLogin() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            if (!username || !password) {
                showNotification('请输入用户名和密码', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('登录成功！正在跳转到仪表板...', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();

                    // 更新导航栏显示用户信息
                    updateNavbarForUser(result.user);

                    // 跳转到仪表板或指定页面
                    setTimeout(() => {
                        const redirectUrl = result.redirect_url || '/dashboard';
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    showNotification(result.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 注册表单提交
        async function submitRegister() {
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const fullName = document.getElementById('registerFullName').value;
            const password = document.getElementById('registerPassword').value;

            if (!username || !email || !password) {
                showNotification('请填写所有必填字段', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, email, full_name: fullName, password })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('注册成功！正在跳转到仪表板...', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();

                    // 更新导航栏显示用户信息
                    updateNavbarForUser(result.user);

                    // 跳转到仪表板或指定页面
                    setTimeout(() => {
                        const redirectUrl = result.redirect_url || '/dashboard';
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    showNotification(result.error || '注册失败', 'error');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 显示登录模态框
        function showLogin() {
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        }

        // 显示注册模态框
        function showRegister() {
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            registerModal.show();
        }
    </script>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 户部尚书专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap">
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>


</body>
</html>
