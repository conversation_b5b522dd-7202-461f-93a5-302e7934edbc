<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--primary) !important;
        }

        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: rgba(99, 102, 241, 0.1);
            border-bottom: 1px solid var(--glass-border);
            border-radius: 16px 16px 0 0 !important;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin: 0 auto 1rem;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            text-align: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: var(--success);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: var(--danger);
        }

        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .badge-primary {
            background: var(--primary);
            color: white;
        }

        .badge-success {
            background: var(--success);
            color: white;
        }

        .badge-warning {
            background: var(--warning);
            color: white;
        }

        .activity-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-muted);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/realtime">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>账户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/security">安全设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-user-circle me-3"></i>
                个人资料
            </h1>
            <p class="page-subtitle">管理您的账户信息和偏好设置</p>
        </div>

        <div class="row">
            <!-- 左侧：个人信息 -->
            <div class="col-lg-4">
                <!-- 头像和基本信息 -->
                <div class="card">
                    <div class="card-body text-center">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h4>量化交易员</h4>
                        <p class="text-muted"><EMAIL></p>
                        <span class="badge badge-success">高级会员</span>
                        <hr>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-number">15</div>
                                <div class="stat-label">策略数</div>
                            </div>
                            <div class="col-4">
                                <div class="stat-number">128</div>
                                <div class="stat-label">回测次数</div>
                            </div>
                            <div class="col-4">
                                <div class="stat-number">23.5%</div>
                                <div class="stat-label">年化收益</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户统计 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>账户统计
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="stat-card">
                            <div class="stat-number">$125,680</div>
                            <div class="stat-label">总资产</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">+$18,450</div>
                            <div class="stat-label">本月收益</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">85.2%</div>
                            <div class="stat-label">胜率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：详细信息和设置 -->
            <div class="col-lg-8">
                <!-- 个人信息编辑 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>编辑资料
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="profileForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" value="量化交易员" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">邮箱地址</label>
                                        <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">手机号码</label>
                                        <input type="tel" class="form-control" id="phone" value="+86 138****8888">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">所在地区</label>
                                        <select class="form-select" id="region">
                                            <option value="cn" selected>中国大陆</option>
                                            <option value="hk">香港</option>
                                            <option value="tw">台湾</option>
                                            <option value="us">美国</option>
                                            <option value="other">其他</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">个人简介</label>
                                <textarea class="form-control" id="bio" rows="3" placeholder="介绍一下您的交易经验和投资理念...">专注于量化交易策略开发，擅长趋势跟踪和均值回归策略。拥有5年金融市场经验，致力于通过数据驱动的方法实现稳定收益。</textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">风险偏好</label>
                                        <select class="form-select" id="riskPreference">
                                            <option value="conservative">保守型</option>
                                            <option value="moderate" selected>稳健型</option>
                                            <option value="aggressive">激进型</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">交易经验</label>
                                        <select class="form-select" id="experience">
                                            <option value="beginner">新手 (< 1年)</option>
                                            <option value="intermediate">中级 (1-3年)</option>
                                            <option value="advanced" selected>高级 (3-5年)</option>
                                            <option value="expert">专家 (> 5年)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="button" class="btn btn-outline-secondary me-2">取消</button>
                                <button type="submit" class="btn btn-primary">保存更改</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-2"></i>最近活动
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-rocket text-primary me-2"></i>
                                    <strong>运行高级回测</strong> - AAPL移动平均策略
                                </div>
                                <div class="activity-time">2小时前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-download text-success me-2"></i>
                                    <strong>下载策略</strong> - RSI均值回归策略
                                </div>
                                <div class="activity-time">5小时前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-heart text-danger me-2"></i>
                                    <strong>收藏策略</strong> - 布林带突破策略
                                </div>
                                <div class="activity-time">1天前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-comments text-info me-2"></i>
                                    <strong>发表评论</strong> - 关于动量策略的讨论
                                </div>
                                <div class="activity-time">2天前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-plus text-warning me-2"></i>
                                    <strong>创建策略</strong> - 多因子选股策略
                                </div>
                                <div class="activity-time">3天前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 用户状态管理
        let currentUser = null;

        // 页面加载时获取用户数据
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadUserProfile();
        });

        // 检查登录状态 - 使用统一的认证API
        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.authenticated && data.user) {
                        currentUser = data.user;
                        updateNavbarForLoggedInUser(data.user);
                    } else {
                        // 未登录，显示提示并重定向到首页
                        showAlert('请先登录', 'warning');
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    }
                } else {
                    // 登录状态检查失败，重定向到首页
                    showAlert('检查登录状态失败', 'error');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                showAlert('网络错误', 'error');
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            }
        }

        // 更新导航栏为已登录用户
        function updateNavbarForLoggedInUser(user) {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>${user.username}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                        <li><a class="dropdown-item text-white active" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item text-white" href="/my-strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                        <li><a class="dropdown-item text-white" href="/settings"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider" style="border-color: var(--glass-border);"></li>
                        <li><a class="dropdown-item text-white" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </li>
            `;
        }

        // 加载用户资料
        async function loadUserProfile() {
            try {
                const response = await fetch('/auth/profile', {
                    credentials: 'include'
                });
                const result = await response.json();

                if (result.success && result.user) {
                    const user = result.user;

                    // 填充表单数据
                    document.getElementById('username').value = user.username || '';
                    document.getElementById('email').value = user.email || '';
                    document.getElementById('phone').value = user.phone || '';
                    document.getElementById('region').value = user.region || 'cn';
                    document.getElementById('bio').value = user.bio || '';
                    document.getElementById('riskPreference').value = user.risk_preference || 'moderate';
                    document.getElementById('experience').value = user.experience || 'beginner';

                    // 更新显示的用户信息
                    const fullNameElement = document.querySelector('.card-body h4');
                    if (fullNameElement) {
                        fullNameElement.textContent = user.full_name || user.username || '用户';
                    }

                    // 更新邮箱显示
                    const emailElement = document.querySelector('.card-body p.text-muted');
                    if (emailElement) {
                        emailElement.textContent = user.email || '';
                    }

                    // 更新会员状态
                    const badgeElement = document.querySelector('.badge');
                    if (badgeElement) {
                        if (user.is_premium) {
                            badgeElement.textContent = 'VIP会员';
                            badgeElement.className = 'badge badge-warning';
                        } else {
                            badgeElement.textContent = '普通用户';
                            badgeElement.className = 'badge badge-secondary';
                        }
                    }

                    // 更新统计数据
                    const statNumbers = document.querySelectorAll('.stat-number');
                    if (statNumbers.length >= 3) {
                        statNumbers[0].textContent = user.total_strategies || '0';
                        statNumbers[1].textContent = user.total_backtests || '0';
                        statNumbers[2].textContent = user.win_rate ? `${user.win_rate}%` : '0%';
                    }
                } else {
                    // 如果获取用户资料失败，可能是登录状态过期
                    console.log('获取用户资料失败，可能需要重新登录');
                    showAlert('登录状态已过期，请重新登录', 'warning');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            } catch (error) {
                console.error('加载用户资料失败:', error);
                showAlert('网络错误，无法加载用户资料', 'error');
            }
        }

        // 表单提交处理
        document.getElementById('profileForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 收集表单数据
            const formData = {
                username: document.getElementById('username').value.trim(),
                email: document.getElementById('email').value.trim(),
                full_name: document.getElementById('username').value.trim(), // 使用用户名作为全名
                phone: document.getElementById('phone').value.trim(),
                region: document.getElementById('region').value,
                bio: document.getElementById('bio').value.trim(),
                risk_preference: document.getElementById('riskPreference').value,
                experience: document.getElementById('experience').value
            };

            // 验证必填字段
            if (!formData.username || !formData.email) {
                showAlert('用户名和邮箱不能为空', 'error');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(formData.email)) {
                showAlert('请输入有效的邮箱地址', 'error');
                return;
            }

            try {
                // 显示加载状态
                const submitBtn = document.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.disabled = true;
                submitBtn.textContent = '保存中...';

                const response = await fetch('/auth/profile', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message || '个人资料已成功更新！', 'success');
                    // 重新加载用户数据以确保显示最新信息
                    setTimeout(() => {
                        loadUserProfile();
                    }, 1000);
                } else {
                    showAlert(result.error || '更新失败', 'error');
                }

                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;

            } catch (error) {
                console.error('更新用户资料失败:', error);
                showAlert('网络错误，请稍后重试', 'error');

                // 恢复按钮状态
                const submitBtn = document.querySelector('button[type="submit"]');
                submitBtn.disabled = false;
                submitBtn.textContent = '保存更改';
            }
        });

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    currentUser = null;
                    showAlert('已退出登录', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showAlert('退出登录失败', 'error');
                }
            } catch (error) {
                console.error('退出登录失败:', error);
                showAlert('退出登录失败', 'error');
            }
        }
    </script>
</body>
</html>
