<!-- 统一登录模态框组件 -->
<div class="modal fade" id="loginModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--glass-bg, rgba(30, 41, 59, 0.8)); backdrop-filter: blur(20px); border: 1px solid var(--glass-border, rgba(255,255,255,0.2));">
            <div class="modal-header border-bottom border-secondary">
                <h5 class="modal-title text-white">用户登录</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="loginForm" onsubmit="event.preventDefault(); performLogin();">
                    <div class="mb-3">
                        <label class="form-label text-white">用户名或邮箱</label>
                        <input type="text" class="form-control" id="loginUsername" required
                               placeholder="请输入用户名或邮箱"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">密码</label>
                        <input type="password" class="form-control" id="loginPassword" required
                               placeholder="请输入密码"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label text-white" for="rememberMe">记住我</label>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">登录</button>
                </form>
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        还没有账户？ 
                        <a href="#" onclick="showRegister()" class="text-primary">立即注册</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统一注册模态框组件 -->
<div class="modal fade" id="registerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--glass-bg, rgba(30, 41, 59, 0.8)); backdrop-filter: blur(20px); border: 1px solid var(--glass-border, rgba(255,255,255,0.2));">
            <div class="modal-header border-bottom border-secondary">
                <h5 class="modal-title text-white">用户注册</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="registerForm" onsubmit="event.preventDefault(); performRegister();">
                    <div class="mb-3">
                        <label class="form-label text-white">用户名</label>
                        <input type="text" class="form-control" id="registerUsername" required
                               placeholder="请输入用户名"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">邮箱</label>
                        <input type="email" class="form-control" id="registerEmail" required
                               placeholder="请输入邮箱地址"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">全名</label>
                        <input type="text" class="form-control" id="registerFullName"
                               placeholder="请输入真实姓名（可选）"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">密码</label>
                        <input type="password" class="form-control" id="registerPassword" required
                               placeholder="请输入密码"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <button type="submit" class="btn btn-primary w-100">注册</button>
                </form>
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        已有账户？ 
                        <a href="#" onclick="showLogin()" class="text-primary">立即登录</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 统一登录逻辑
async function performLogin() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe')?.checked || false;

    if (!username || !password) {
        showNotification('请填写完整的登录信息', 'error');
        return;
    }

    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,  // 后端会自动识别用户名或邮箱
                password: password,
                remember_me: rememberMe
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('登录成功！', 'success');
            
            // 关闭模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) {
                loginModal.hide();
            }
            
            // 延迟刷新页面以显示通知
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(result.error || '登录失败', 'error');
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        showNotification('网络错误，请稍后重试', 'error');
    }
}

// 统一注册逻辑
async function performRegister() {
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const fullName = document.getElementById('registerFullName').value.trim();
    const password = document.getElementById('registerPassword').value;

    if (!username || !email || !password) {
        showNotification('请填写必填信息', 'error');
        return;
    }

    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('注册成功！正在跳转...', 'success');
            
            // 关闭模态框
            const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
            if (registerModal) {
                registerModal.hide();
            }
            
            // 延迟跳转
            setTimeout(() => {
                window.location.href = result.redirect_url || '/dashboard';
            }, 1000);
        } else {
            showNotification(result.error || '注册失败', 'error');
        }
    } catch (error) {
        console.error('注册请求失败:', error);
        showNotification('网络错误，请稍后重试', 'error');
    }
}

// 显示登录模态框
function showLogin() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// 显示注册模态框
function showRegister() {
    // 先关闭登录模态框
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }
    
    // 显示注册模态框
    setTimeout(() => {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    }, 300);
}

// 通知函数（如果页面没有定义的话）
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}
</script>
