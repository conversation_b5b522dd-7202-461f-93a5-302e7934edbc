<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantTradeX - 专业量化交易策略平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* 导航栏 */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--primary);
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--text-primary) !important;
            background: rgba(99, 102, 241, 0.1);
        }

        /* Hero Section */
        .hero {
            padding: 8rem 0 6rem;
            text-align: center;
            position: relative;
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-weight: 400;
        }

        .hero-description {
            font-size: 1.1rem;
            color: var(--text-muted);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* 按钮 */
        .btn-modern {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            border: none;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
            margin: 0.5rem;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
            color: white;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--glass-border);
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            color: var(--text-primary);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--primary);
            color: var(--primary);
            transform: translateY(-2px);
        }

        /* 统计卡片 */
        .stats-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem 2rem;
            margin: 4rem 0;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 功能卡片 */
        .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2.5rem;
            height: 100%;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
            border-color: rgba(99, 102, 241, 0.3);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
        }

        .feature-card h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .feature-card p {
            color: var(--text-secondary);
            line-height: 1.7;
            font-size: 1rem;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .hero {
                padding: 4rem 0 3rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .stats-section {
                padding: 2rem 1rem;
            }

            .feature-card {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i>
                QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item" id="strategyEditorNav" style="display: none;">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/realtime">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1 class="hero-title">QuantTradeX</h1>
            <p class="hero-subtitle">专业的量化交易策略开发与回测平台</p>
            <p class="hero-description">
                集成多品类金融资产数据，提供策略开发、回测分析、实盘交易一站式解决方案
            </p>

            <div class="mt-4">
                <a href="/dashboard" class="btn-modern">
                    <i class="fas fa-rocket"></i>
                    开始交易
                </a>
                <a href="/strategies" class="btn-outline">
                    <i class="fas fa-search"></i>
                    浏览策略
                </a>
            </div>
        </div>
    </section>

    <!-- 统计数据 -->
    <div class="container">
        <div class="stats-section">
            <div class="row g-4">
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" id="userCount">1,000+</div>
                        <div class="stat-label">活跃用户</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" id="strategyCount">500+</div>
                        <div class="stat-label">交易策略</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" id="backtestCount">10,000+</div>
                        <div class="stat-label">回测次数</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" id="returnRate">15.8%</div>
                        <div class="stat-label">平均年化收益</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能特色 -->
    <div class="container my-5">
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <h4>用户管理</h4>
                    <p>完善的用户注册登录系统，支持多因素认证，权限分级管理，保障账户安全。</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h4>数据中心</h4>
                    <p>实时行情数据、历史数据查询、技术指标计算，支持股票、期货、外汇、数字货币等多品类资产。</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h4>策略开发</h4>
                    <p>在线代码编辑器，丰富的策略模板库，调试工具，支持Python和JavaScript策略开发。</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h4>回测系统</h4>
                    <p>历史数据回测，参数优化，性能分析，风险评估，全面的回测报告生成。</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h4>实盘交易</h4>
                    <p>多交易所集成，订单管理系统，风险控制设置，实时交易执行和监控。</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h4>策略市场</h4>
                    <p>策略分享平台，评分系统，订阅购买，策略作者收益分成，构建策略生态。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速体验区 -->
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h4>立即开始您的量化交易之旅</h4>
                    <p class="mb-4">选择您感兴趣的功能模块，体验专业的量化交易平台</p>

                    <div class="row g-3">
                        <div class="col-md-3 col-6">
                            <a href="/dashboard" class="btn-outline w-100">
                                <i class="fas fa-chart-line d-block mb-2"></i>
                                交易仪表板
                            </a>
                        </div>
                        <div class="col-md-3 col-6">
                            <a href="/strategies" class="btn-outline w-100">
                                <i class="fas fa-store d-block mb-2"></i>
                                策略市场
                            </a>
                        </div>
                        <div class="col-md-3 col-6" id="strategyEditorBtn" style="display: none;">
                            <a href="/strategy-editor" class="btn-outline w-100">
                                <i class="fas fa-code d-block mb-2"></i>
                                策略开发
                            </a>
                        </div>
                        <div class="col-md-3 col-6">
                            <a href="/backtest" class="btn-outline w-100">
                                <i class="fas fa-history d-block mb-2"></i>
                                基础回测
                            </a>
                        </div>
                        <div class="col-md-3 col-6">
                            <a href="/advanced_backtest" class="btn-outline w-100">
                                <i class="fas fa-rocket d-block mb-2"></i>
                                高级回测
                            </a>
                        </div>
                        <div class="col-md-3 col-6">
                            <a href="/realtime" class="btn-outline w-100">
                                <i class="fas fa-broadcast-tower d-block mb-2"></i>
                                实时数据
                            </a>
                        </div>
                        <div class="col-md-3 col-6">
                            <a href="/forum" class="btn-outline w-100">
                                <i class="fas fa-comments d-block mb-2"></i>
                                社区论坛
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 户部尚书专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap">
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 登录和注册逻辑已在统一组件中定义
        // 重写performLogin函数以支持首页特殊逻辑
        window.originalPerformLogin = window.performLogin;
        window.performLogin = async function() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value;
            const rememberMe = document.getElementById('rememberMe')?.checked || false;

            if (!username || !password) {
                showNotification('请填写完整的登录信息', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,  // 后端会自动识别用户名或邮箱
                        password: password,
                        remember_me: rememberMe
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('登录成功！', 'success');

                    // 关闭模态框
                    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
                    if (loginModal) {
                        loginModal.hide();
                    }

                    // 更新导航栏显示用户信息
                    updateNavbarForUser(result.user);

                    // 首页登录不跳转，只更新界面
                } else {
                    showNotification(result.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        };

        // 重写performRegister函数以支持首页特殊逻辑
        window.originalPerformRegister = window.performRegister;
        window.performRegister = async function() {
            const username = document.getElementById('registerUsername').value.trim();
            const email = document.getElementById('registerEmail').value.trim();
            const fullName = document.getElementById('registerFullName').value.trim();
            const password = document.getElementById('registerPassword').value;

            if (!username || !email || !password) {
                showNotification('请填写必填信息', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        full_name: fullName,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('注册成功！正在跳转...', 'success');

                    // 关闭模态框
                    const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
                    if (registerModal) {
                        registerModal.hide();
                    }

                    // 延迟跳转
                    setTimeout(() => {
                        window.location.href = result.redirect_url || '/dashboard';
                    }, 1000);
                } else {
                    showNotification(result.error || '注册失败', 'error');
                }
            } catch (error) {
                console.error('注册请求失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        };

        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            checkLoginStatus();

            // 添加滚动动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有功能卡片
            document.querySelectorAll('.feature-card, .stat-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // 数字动画
            animateNumbers();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/check');
                const result = await response.json();

                if (result.success && result.authenticated && result.user) {
                    // 用户已登录，更新界面
                    updateNavbarForUser(result.user);
                }
            } catch (error) {
                // 用户未登录或检查失败，保持默认状态
                console.log('用户未登录');
            }
        }

        // 数字动画效果
        function animateNumbers() {
            const numbers = [
                { element: 'userCount', target: 1000, suffix: '+' },
                { element: 'strategyCount', target: 500, suffix: '+' },
                { element: 'backtestCount', target: 10000, suffix: '+' },
                { element: 'returnRate', target: 15.8, suffix: '%' }
            ];

            numbers.forEach(({ element, target, suffix }) => {
                const el = document.getElementById(element);
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    el.textContent = Math.floor(current) + suffix;
                }, 20);
            });
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 更新导航栏用户信息
        function updateNavbarForUser(user) {
            const navbarNav = document.querySelector('.navbar-nav:last-child');
            if (navbarNav && user) {
                // 显示策略开发入口
                const strategyEditorNav = document.getElementById('strategyEditorNav');
                const strategyEditorBtn = document.getElementById('strategyEditorBtn');
                if (strategyEditorNav) strategyEditorNav.style.display = 'block';
                if (strategyEditorBtn) strategyEditorBtn.style.display = 'block';

                navbarNav.innerHTML = `
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>${user.username}
                            ${user.is_premium ? '<span class="badge bg-warning ms-1">VIP</span>' : ''}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>仪表板</a></li>
                            <li><a class="dropdown-item" href="/strategy-editor"><i class="fas fa-code me-2"></i>策略开发</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showProfile()"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showMyStrategies()"><i class="fas fa-list me-2"></i>我的策略</a></li>
                            ${!user.is_premium ? '<li><a class="dropdown-item" href="#" onclick="showUpgrade()"><i class="fas fa-crown me-2"></i>升级VIP</a></li>' : ''}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                `;
            }
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('已退出登录', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }

        // 显示个人资料
        function showProfile() {
            showNotification('个人资料功能开发中...', 'info');
        }

        // 显示我的策略
        function showMyStrategies() {
            window.location.href = '/my-strategies';
        }

        // 显示升级VIP
        function showUpgrade() {
            // 创建VIP升级模态框
            const upgradeModal = document.createElement('div');
            upgradeModal.className = 'modal fade';
            upgradeModal.id = 'upgradeModal';
            upgradeModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                        <div class="modal-header border-bottom border-secondary">
                            <h5 class="modal-title text-white">
                                <i class="fas fa-crown text-warning me-2"></i>升级VIP会员
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card h-100" style="background: rgba(255,255,255,0.05); border: 1px solid var(--glass-border);">
                                        <div class="card-body text-center">
                                            <h6 class="card-title text-white">月度会员</h6>
                                            <div class="display-6 text-warning mb-3">¥99.99</div>
                                            <ul class="list-unstyled text-secondary">
                                                <li><i class="fas fa-check text-success me-2"></i>访问所有付费策略</li>
                                                <li><i class="fas fa-check text-success me-2"></i>高级回测功能</li>
                                                <li><i class="fas fa-check text-success me-2"></i>实时数据推送</li>
                                                <li><i class="fas fa-check text-success me-2"></i>专属客服支持</li>
                                            </ul>
                                            <button class="btn btn-warning w-100" onclick="upgradeVIP('monthly')">
                                                立即升级
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100" style="background: rgba(255,255,255,0.05); border: 2px solid var(--warning);">
                                        <div class="card-body text-center">
                                            <h6 class="card-title text-warning">
                                                年度会员 <span class="badge bg-danger">推荐</span>
                                            </h6>
                                            <div class="display-6 text-warning mb-3">¥999.99</div>
                                            <ul class="list-unstyled text-secondary">
                                                <li><i class="fas fa-check text-success me-2"></i>月度会员所有功能</li>
                                                <li><i class="fas fa-check text-success me-2"></i>策略收益分成</li>
                                                <li><i class="fas fa-check text-success me-2"></i>API接口访问</li>
                                                <li><i class="fas fa-check text-success me-2"></i>优先技术支持</li>
                                            </ul>
                                            <button class="btn btn-warning w-100" onclick="upgradeVIP('yearly')">
                                                立即升级
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(upgradeModal);
            new bootstrap.Modal(upgradeModal).show();

            // 模态框关闭时移除元素
            upgradeModal.addEventListener('hidden.bs.modal', function() {
                upgradeModal.remove();
            });
        }

        // VIP升级处理
        async function upgradeVIP(plan) {
            try {
                // 显示支付方式选择
                const paymentMethod = await showPaymentMethodSelection();
                if (!paymentMethod) {
                    return; // 用户取消
                }

                const response = await fetch('/auth/upgrade', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        plan: plan,
                        payment_method: paymentMethod
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('支付订单已创建，正在跳转到支付页面...', 'info');
                    bootstrap.Modal.getInstance(document.getElementById('upgradeModal')).hide();

                    // 跳转到支付页面
                    setTimeout(() => {
                        window.open(result.payment_url, '_blank');
                    }, 1000);
                } else {
                    showNotification(result.error || '创建订单失败', 'error');
                }
            } catch (error) {
                console.error('升级VIP错误:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 显示支付方式选择
        async function showPaymentMethodSelection() {
            try {
                // 获取支持的支付方式
                const response = await fetch('/api/payment/methods');
                const result = await response.json();

                if (!result.success) {
                    showNotification('获取支付方式失败', 'error');
                    return null;
                }

                const methods = result.methods.filter(method => method.status === 'active');

                if (methods.length === 0) {
                    showNotification('暂无可用的支付方式', 'warning');
                    return null;
                }

                // 创建支付方式选择模态框
                const modalHtml = `
                    <div class="modal fade" id="paymentMethodModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
                                <div class="modal-header">
                                    <h5 class="modal-title">选择支付方式</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        ${methods.map(method => `
                                            <div class="col-md-6 mb-3">
                                                <div class="card payment-method-card" onclick="selectPaymentMethod('${method.id}')" style="cursor: pointer; border: 2px solid transparent;">
                                                    <div class="card-body text-center">
                                                        <i class="fas ${getPaymentIcon(method.id)} fa-2x mb-2"></i>
                                                        <h6>${method.name}</h6>
                                                        ${method.id === 'mock' ? '<small class="text-muted">测试支付</small>' : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 添加模态框到页面
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // 显示模态框
                const paymentModal = new bootstrap.Modal(document.getElementById('paymentMethodModal'));
                paymentModal.show();

                // 返回Promise，等待用户选择
                return new Promise((resolve) => {
                    window.paymentMethodResolve = resolve;

                    // 监听模态框关闭事件
                    document.getElementById('paymentMethodModal').addEventListener('hidden.bs.modal', () => {
                        if (window.paymentMethodResolve) {
                            window.paymentMethodResolve(null);
                            window.paymentMethodResolve = null;
                        }
                        document.getElementById('paymentMethodModal').remove();
                    });
                });

            } catch (error) {
                console.error('获取支付方式失败:', error);
                showNotification('获取支付方式失败', 'error');
                return null;
            }
        }

        // 选择支付方式
        function selectPaymentMethod(methodId) {
            // 高亮选中的支付方式
            document.querySelectorAll('.payment-method-card').forEach(card => {
                card.style.borderColor = 'transparent';
            });
            event.currentTarget.style.borderColor = '#28a745';

            // 延迟关闭模态框并返回选择
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('paymentMethodModal')).hide();
                if (window.paymentMethodResolve) {
                    window.paymentMethodResolve(methodId);
                    window.paymentMethodResolve = null;
                }
            }, 300);
        }

        // 获取支付方式图标
        function getPaymentIcon(methodId) {
            const icons = {
                'alipay': 'fa-alipay',
                'wechat': 'fa-weixin',
                'paypal': 'fa-paypal',
                'stripe': 'fa-credit-card',
                'mock': 'fa-coins'
            };
            return icons[methodId] || 'fa-credit-card';
        }

        // 用户状态管理
        let currentUser = null;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/api/user/status', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.logged_in) {
                        currentUser = data.user;
                        updateNavbarForLoggedInUser(data.user);
                        showMemberFeatures();
                    } else {
                        updateNavbarForGuest();
                        hideMemberFeatures();
                    }
                } else {
                    updateNavbarForGuest();
                    hideMemberFeatures();
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                updateNavbarForGuest();
                hideMemberFeatures();
            }
        }

        // 更新导航栏为已登录用户
        function updateNavbarForLoggedInUser(user) {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>${user.username}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                        <li><a class="dropdown-item text-white" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item text-white" href="/my-strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                        <li><a class="dropdown-item text-white" href="/settings"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider" style="border-color: var(--glass-border);"></li>
                        <li><a class="dropdown-item text-white" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </li>
            `;
        }

        // 更新导航栏为访客
        function updateNavbarForGuest() {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showLogin()">
                        <i class="fas fa-sign-in-alt me-1"></i>登录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showRegister()">
                        <i class="fas fa-user-plus me-1"></i>注册
                    </a>
                </li>
            `;
        }

        // 显示会员功能
        function showMemberFeatures() {
            const strategyEditorNav = document.getElementById('strategyEditorNav');
            const strategyEditorBtn = document.getElementById('strategyEditorBtn');
            if (strategyEditorNav) strategyEditorNav.style.display = 'block';
            if (strategyEditorBtn) strategyEditorBtn.style.display = 'block';
        }

        // 隐藏会员功能
        function hideMemberFeatures() {
            const strategyEditorNav = document.getElementById('strategyEditorNav');
            const strategyEditorBtn = document.getElementById('strategyEditorBtn');
            if (strategyEditorNav) strategyEditorNav.style.display = 'none';
            if (strategyEditorBtn) strategyEditorBtn.style.display = 'none';
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    currentUser = null;
                    updateNavbarForGuest();
                    hideMemberFeatures();
                    showNotification('已退出登录', 'success');
                } else {
                    showNotification('退出登录失败', 'error');
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }

    </script>
</body>
</html>
