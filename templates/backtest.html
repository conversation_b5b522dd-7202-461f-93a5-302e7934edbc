<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测系统 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* 导航栏 */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--primary);
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary) !important;
            background: rgba(99, 102, 241, 0.1);
        }

        /* 卡片样式 */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 表单样式 */
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            color: var(--text-primary);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-outline-light {
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--primary);
            color: var(--primary);
            transform: translateY(-2px);
        }

        /* 进度条 */
        .progress {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            height: 8px;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary), var(--accent));
            border-radius: 10px;
        }

        /* 指标卡片 */
        .metric-card {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .positive {
            color: var(--success);
        }

        .negative {
            color: var(--danger);
        }

        .neutral {
            color: var(--warning);
        }

        /* 代码编辑器 */
        .code-editor {
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            border-radius: 10px;
            padding: 1rem;
            min-height: 200px;
            border: 1px solid var(--glass-border);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 页面标题 */
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">策略市场</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/backtest">回测系统</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">社区论坛</a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-4">
        <!-- 页面标题 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="page-title">
                    <i class="fas fa-history me-3"></i>回测系统
                </h1>
                <p class="page-subtitle">测试您的交易策略在历史数据上的表现，优化投资决策</p>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：回测配置 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>回测配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="backtestForm">
                            <div class="mb-3">
                                <label class="form-label">回测名称</label>
                                <input type="text" class="form-control" id="backtestName"
                                       placeholder="输入回测名称" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">交易标的</label>
                                <input type="text" class="form-control" id="symbol"
                                       placeholder="例如: AAPL, GOOGL" required>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">初始资金</label>
                                <input type="number" class="form-control" id="initialCapital"
                                       value="100000" min="1000" step="1000" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">策略选择</label>
                                <select class="form-select" id="strategySelect">
                                    <option value="">选择已有策略</option>
                                    <option value="custom">自定义策略</option>
                                </select>
                            </div>

                            <div class="mb-3" id="customStrategySection" style="display: none;">
                                <label class="form-label">策略代码</label>
                                <textarea class="form-control code-editor" id="strategyCode" rows="8"
                                          placeholder="# 简单移动平均策略示例&#10;def strategy(data):&#10;    # 计算20日和50日移动平均&#10;    sma_20 = data['close'].rolling(20).mean()&#10;    sma_50 = data['close'].rolling(50).mean()&#10;    &#10;    # 生成交易信号&#10;    signals = []&#10;    for i in range(len(data)):&#10;        if sma_20[i] > sma_50[i]:&#10;            signals.append('buy')&#10;        else:&#10;            signals.append('sell')&#10;    &#10;    return signals"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">策略参数 (JSON格式)</label>
                                <textarea class="form-control" id="strategyParams" rows="3"
                                          placeholder='{"param1": "value1", "param2": "value2"}'>{}</textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-play me-2"></i>开始回测
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 回测历史 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clock me-2"></i>回测历史
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="backtestHistory">
                            <div class="text-center text-white-50">
                                <i class="fas fa-history fa-2x mb-2"></i>
                                <p>暂无回测记录</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：回测结果 -->
            <div class="col-md-8">
                <!-- 回测进度 -->
                <div class="card" id="progressCard" style="display: none;">
                    <div class="card-body">
                        <h6>回测进行中...</h6>
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                        </div>
                        <small class="text-white-50" id="progressText">准备中...</small>
                    </div>
                </div>

                <!-- 回测结果 -->
                <div class="card" id="resultsCard" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>回测结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 关键指标 -->
                        <div class="row mb-4" id="metricsRow">
                            <!-- 指标卡片将通过JavaScript生成 -->
                        </div>

                        <!-- 收益曲线图 -->
                        <div class="mb-4">
                            <h6>收益曲线</h6>
                            <canvas id="returnsChart" height="100"></canvas>
                        </div>

                        <!-- 详细统计 -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6>交易统计</h6>
                                <div id="tradeStats">
                                    <!-- 交易统计将通过JavaScript生成 -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>风险指标</h6>
                                <div id="riskMetrics">
                                    <!-- 风险指标将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 默认显示 -->
                <div class="card" id="defaultCard">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-4x text-white-50 mb-3"></i>
                        <h4 class="text-white">开始您的第一次回测</h4>
                        <p class="text-white-50">在左侧配置回测参数，然后点击"开始回测"按钮</p>

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="metric-card">
                                    <i class="fas fa-database fa-2x mb-2 text-primary"></i>
                                    <div class="metric-label">历史数据</div>
                                    <div class="metric-value">10年+</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="metric-card">
                                    <i class="fas fa-lightning-bolt fa-2x mb-2 text-warning"></i>
                                    <div class="metric-label">回测速度</div>
                                    <div class="metric-value">秒级</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="metric-card">
                                    <i class="fas fa-shield-alt fa-2x mb-2 text-success"></i>
                                    <div class="metric-label">数据准确性</div>
                                    <div class="metric-value">99.9%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentBacktest = null;
        let returnsChart = null;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期
            const endDate = new Date();
            const startDate = new Date();
            startDate.setFullYear(endDate.getFullYear() - 1);

            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

            // 策略选择事件
            document.getElementById('strategySelect').addEventListener('change', function() {
                const customSection = document.getElementById('customStrategySection');
                customSection.style.display = this.value === 'custom' ? 'block' : 'none';
            });

            // 表单提交事件
            document.getElementById('backtestForm').addEventListener('submit', function(e) {
                e.preventDefault();
                runBacktest();
            });

            // 加载回测历史
            loadBacktestHistory();
        });

        // 运行回测
        async function runBacktest() {
            const formData = {
                name: document.getElementById('backtestName').value,
                symbol: document.getElementById('symbol').value.toUpperCase(),
                start_date: document.getElementById('startDate').value,
                end_date: document.getElementById('endDate').value,
                initial_capital: parseFloat(document.getElementById('initialCapital').value),
                strategy_code: document.getElementById('strategyCode').value,
                parameters: JSON.parse(document.getElementById('strategyParams').value || '{}')
            };

            // 显示进度
            showProgress();

            try {
                const response = await fetch('/api/backtest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    currentBacktest = result.backtest;
                    displayResults(result.backtest);
                    loadBacktestHistory(); // 刷新历史记录
                } else {
                    hideProgress();
                    showError(result.error || '回测失败');
                }
            } catch (error) {
                hideProgress();
                console.error('回测失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示进度
        function showProgress() {
            document.getElementById('defaultCard').style.display = 'none';
            document.getElementById('resultsCard').style.display = 'none';
            document.getElementById('progressCard').style.display = 'block';

            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 90) progress = 90;

                progressBar.style.width = progress + '%';

                if (progress < 30) {
                    progressText.textContent = '获取历史数据...';
                } else if (progress < 60) {
                    progressText.textContent = '执行策略逻辑...';
                } else if (progress < 90) {
                    progressText.textContent = '计算回测指标...';
                }
            }, 200);

            // 模拟回测完成
            setTimeout(() => {
                clearInterval(interval);
                progressBar.style.width = '100%';
                progressText.textContent = '回测完成！';
            }, 2000);
        }

        // 隐藏进度
        function hideProgress() {
            document.getElementById('progressCard').style.display = 'none';
            document.getElementById('defaultCard').style.display = 'block';
        }

        // 显示回测结果
        function displayResults(backtest) {
            document.getElementById('progressCard').style.display = 'none';
            document.getElementById('defaultCard').style.display = 'none';
            document.getElementById('resultsCard').style.display = 'block';

            const results = backtest.results;

            // 显示关键指标
            displayMetrics(results);

            // 绘制收益曲线
            drawReturnsChart(results);

            // 显示详细统计
            displayDetailedStats(results);
        }

        // 显示关键指标
        function displayMetrics(results) {
            const metricsRow = document.getElementById('metricsRow');

            const metrics = [
                {
                    label: '总收益率',
                    value: results.total_return?.toFixed(2) + '%',
                    class: results.total_return >= 0 ? 'positive' : 'negative'
                },
                {
                    label: '最终价值',
                    value: '$' + (results.final_value?.toLocaleString() || '0'),
                    class: 'metric-value'
                },
                {
                    label: '最大回撤',
                    value: results.max_drawdown?.toFixed(2) + '%',
                    class: 'negative'
                },
                {
                    label: '夏普比率',
                    value: results.sharpe_ratio?.toFixed(2) || '0.00',
                    class: 'metric-value'
                },
                {
                    label: '交易次数',
                    value: results.trades_count || '0',
                    class: 'metric-value'
                },
                {
                    label: '胜率',
                    value: results.win_rate?.toFixed(1) + '%',
                    class: results.win_rate >= 50 ? 'positive' : 'negative'
                }
            ];

            metricsRow.innerHTML = metrics.map(metric => `
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value ${metric.class}">${metric.value}</div>
                        <div class="metric-label">${metric.label}</div>
                    </div>
                </div>
            `).join('');
        }

        // 绘制收益曲线
        function drawReturnsChart(results) {
            const ctx = document.getElementById('returnsChart').getContext('2d');

            // 销毁现有图表
            if (returnsChart) {
                returnsChart.destroy();
            }

            // 模拟收益曲线数据
            const dates = [];
            const returns = [];
            const startDate = new Date(results.start_date);
            const endDate = new Date(results.end_date);
            const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

            for (let i = 0; i <= totalDays; i += Math.ceil(totalDays / 50)) {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);
                dates.push(date.toLocaleDateString());

                // 模拟收益曲线
                const progress = i / totalDays;
                const volatility = Math.sin(progress * Math.PI * 4) * 0.1;
                const trend = progress * (results.total_return / 100);
                returns.push((trend + volatility) * 100);
            }

            returnsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: '累计收益率 (%)',
                        data: returns,
                        borderColor: results.total_return >= 0 ? '#4ade80' : '#ef4444',
                        backgroundColor: results.total_return >= 0 ? 'rgba(74, 222, 128, 0.1)' : 'rgba(239, 68, 68, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: 'white'
                            }
                        },
                        y: {
                            ticks: {
                                color: 'white'
                            }
                        }
                    }
                }
            });
        }

        // 显示详细统计
        function displayDetailedStats(results) {
            const tradeStats = document.getElementById('tradeStats');
            const riskMetrics = document.getElementById('riskMetrics');

            tradeStats.innerHTML = `
                <ul class="list-unstyled">
                    <li><strong>总交易次数:</strong> ${results.trades_count || 0}</li>
                    <li><strong>盈利交易:</strong> ${Math.round((results.trades_count || 0) * (results.win_rate || 0) / 100)}</li>
                    <li><strong>亏损交易:</strong> ${(results.trades_count || 0) - Math.round((results.trades_count || 0) * (results.win_rate || 0) / 100)}</li>
                    <li><strong>胜率:</strong> ${results.win_rate?.toFixed(1) || 0}%</li>
                    <li><strong>平均收益:</strong> ${(results.total_return / (results.trades_count || 1))?.toFixed(2) || 0}%</li>
                </ul>
            `;

            riskMetrics.innerHTML = `
                <ul class="list-unstyled">
                    <li><strong>最大回撤:</strong> ${results.max_drawdown?.toFixed(2) || 0}%</li>
                    <li><strong>夏普比率:</strong> ${results.sharpe_ratio?.toFixed(2) || 0}</li>
                    <li><strong>波动率:</strong> ${(Math.abs(results.total_return) * 0.3)?.toFixed(2) || 0}%</li>
                    <li><strong>VaR (95%):</strong> ${(results.total_return * -0.05)?.toFixed(2) || 0}%</li>
                    <li><strong>卡尔马比率:</strong> ${(results.total_return / Math.abs(results.max_drawdown || 1))?.toFixed(2) || 0}</li>
                </ul>
            `;
        }

        // 加载回测历史
        async function loadBacktestHistory() {
            try {
                const response = await fetch('/api/backtests');
                const result = await response.json();

                if (result.success) {
                    displayBacktestHistory(result.backtests);
                }
            } catch (error) {
                console.error('加载回测历史失败:', error);
            }
        }

        // 显示回测历史
        function displayBacktestHistory(backtests) {
            const container = document.getElementById('backtestHistory');

            if (backtests.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-white-50">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p>暂无回测记录</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = backtests.map(backtest => `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${backtest.name}</h6>
                            <small class="text-white-50">${backtest.symbol} | ${new Date(backtest.created_at).toLocaleDateString()}</small>
                        </div>
                        <div class="text-end">
                            <div class="metric-value ${backtest.results?.total_return >= 0 ? 'positive' : 'negative'}">
                                ${backtest.results?.total_return?.toFixed(1) || 0}%
                            </div>
                            <button class="btn btn-sm btn-outline-light" onclick="loadBacktest(${backtest.id})">
                                查看
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 加载特定回测
        function loadBacktest(backtestId) {
            // 这里可以实现加载特定回测的逻辑
            alert('加载回测 ID: ' + backtestId);
        }

        // 显示错误消息
        function showError(message) {
            showNotification(message, 'error');
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 显示登录模态框
        function showLogin() {
            new bootstrap.Modal(document.getElementById('loginModal')).show();
        }

        // 显示注册模态框
        function showRegister() {
            new bootstrap.Modal(document.getElementById('registerModal')).show();
        }

        // 登录表单提交
        async function submitLogin() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            if (!username || !password) {
                showNotification('请输入用户名和密码', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('登录成功！', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(result.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 注册表单提交
        async function submitRegister() {
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const fullName = document.getElementById('registerFullName').value;
            const password = document.getElementById('registerPassword').value;

            if (!username || !email || !password) {
                showNotification('请填写所有必填字段', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, email, full_name: fullName, password })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('注册成功！请登录', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
                    setTimeout(() => {
                        showLogin();
                    }, 1000);
                } else {
                    showNotification(result.error || '注册失败', 'error');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/profile');
                const result = await response.json();

                if (result.success) {
                    updateNavbarForUser(result.user);
                }
            } catch (error) {
                // 用户未登录，忽略错误
            }
        }

        // 更新导航栏用户信息
        function updateNavbarForUser(user) {
            const navbarNav = document.querySelector('.navbar-nav:last-child');
            if (navbarNav && user) {
                navbarNav.innerHTML = `
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>${user.username}
                            ${user.is_premium ? '<span class="badge bg-warning ms-1">VIP</span>' : ''}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>仪表板</a></li>
                            <li><a class="dropdown-item" href="/strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                            ${!user.is_premium ? '<li><a class="dropdown-item" href="#" onclick="showUpgrade()"><i class="fas fa-crown me-2"></i>升级VIP</a></li>' : ''}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                `;
            }
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('已退出登录', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });
    </script>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 户部尚书专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap">
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户注册</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" onsubmit="event.preventDefault(); submitRegister();">
                        <div class="mb-3">
                            <label class="form-label text-white">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">全名</label>
                            <input type="text" class="form-control" id="registerFullName"
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 用户状态管理
        let currentUser = null;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/api/user/status', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.logged_in) {
                        currentUser = data.user;
                        updateNavbarForLoggedInUser(data.user);
                    } else {
                        updateNavbarForGuest();
                    }
                } else {
                    updateNavbarForGuest();
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                updateNavbarForGuest();
            }
        }

        // 更新导航栏为已登录用户
        function updateNavbarForLoggedInUser(user) {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>${user.username}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                        <li><a class="dropdown-item text-white" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item text-white" href="/my-strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                        <li><a class="dropdown-item text-white" href="/settings"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider" style="border-color: var(--glass-border);"></li>
                        <li><a class="dropdown-item text-white" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </li>
            `;
        }

        // 更新导航栏为访客
        function updateNavbarForGuest() {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showLogin()">
                        <i class="fas fa-sign-in-alt me-1"></i>登录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showRegister()">
                        <i class="fas fa-user-plus me-1"></i>注册
                    </a>
                </li>
            `;
        }

        // 显示登录模态框
        function showLogin() {
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        }

        // 显示注册模态框
        function showRegister() {
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            registerModal.show();
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    currentUser = null;
                    updateNavbarForGuest();
                    showNotification('已退出登录', 'success');
                } else {
                    showNotification('退出登录失败', 'error');
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
