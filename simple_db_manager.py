"""
简化的数据库管理器 - 不依赖Redis
专门用于修复邮箱登录问题
"""

import psycopg2
import psycopg2.extras
import hashlib
import logging
from typing import Dict, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleDatabaseManager:
    """简化的数据库管理器"""
    
    def __init__(self):
        self.pg_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'quanttradex_user',
            'password': 'quanttradex_2024!',
            'database': 'quanttradex'
        }
        
        self.pg_conn = None
        self._connect()
    
    def _connect(self):
        """连接数据库"""
        try:
            # 连接PostgreSQL
            self.pg_conn = psycopg2.connect(**self.pg_config)
            self.pg_conn.autocommit = True
            logger.info("PostgreSQL连接成功")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _get_cursor(self):
        """获取PostgreSQL游标"""
        if not self.pg_conn or self.pg_conn.closed:
            self._connect()
        return self.pg_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return self.hash_password(password) == password_hash
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                SELECT id, username, email, password_hash, full_name, role, 
                       is_premium, premium_expires, created_at, last_login,
                       is_active, email_verified, two_factor_enabled
                FROM users WHERE username = %s AND is_active = TRUE
            """, (username,))
            
            user = cursor.fetchone()
            cursor.close()
            
            if user:
                return dict(user)
            
            return None
            
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """根据邮箱获取用户"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                SELECT id, username, email, password_hash, full_name, role, 
                       is_premium, premium_expires, created_at, last_login,
                       is_active, email_verified, two_factor_enabled
                FROM users WHERE email = %s AND is_active = TRUE
            """, (email,))
            
            user = cursor.fetchone()
            cursor.close()
            
            if user:
                return dict(user)
            
            return None
            
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def authenticate_user(self, login_identifier: str, password: str) -> Optional[Dict]:
        """用户认证 - 支持用户名或邮箱登录"""
        try:
            # 首先尝试用户名登录
            user = self.get_user_by_username(login_identifier)
            
            # 如果用户名不存在，尝试邮箱登录
            if not user:
                user = self.get_user_by_email(login_identifier)
            
            if not user:
                return None
            
            # 验证密码
            if not self.verify_password(password, user['password_hash']):
                return None
            
            # 更新最后登录时间
            self.update_last_login(user['username'])
            
            # 移除密码哈希后返回
            user_info = {k: v for k, v in user.items() if k != 'password_hash'}
            return user_info
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return None
    
    def update_last_login(self, username: str):
        """更新最后登录时间"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                UPDATE users SET last_login = CURRENT_TIMESTAMP 
                WHERE username = %s
            """, (username,))
            cursor.close()
            
        except Exception as e:
            logger.error(f"更新登录时间失败: {e}")
    
    def create_user(self, username: str, email: str, password: str, 
                   full_name: str = None, role: str = 'user') -> Dict:
        """创建用户"""
        try:
            cursor = self._get_cursor()
            password_hash = self.hash_password(password)
            
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name, role, email_verified)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id, username, email, full_name, role, is_premium, created_at
            """, (username, email, password_hash, full_name, role, True))
            
            user = dict(cursor.fetchone())
            cursor.close()
            
            logger.info(f"用户创建成功: {username}")
            return user
            
        except psycopg2.IntegrityError as e:
            if 'username' in str(e):
                raise ValueError("用户名已存在")
            elif 'email' in str(e):
                raise ValueError("邮箱已存在")
            else:
                raise ValueError("用户创建失败")
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.pg_conn:
                self.pg_conn.close()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")

# 全局简化数据库管理器实例
simple_db_manager = None

def get_simple_db_manager():
    """获取简化数据库管理器实例"""
    global simple_db_manager
    if simple_db_manager is None:
        simple_db_manager = SimpleDatabaseManager()
    return simple_db_manager

# 测试函数
def test_simple_manager():
    """测试简化管理器"""
    try:
        print("=== 测试简化数据库管理器 ===")
        db = get_simple_db_manager()
        
        # 测试邮箱查询
        print("测试邮箱查询...")
        user = db.get_user_by_email('<EMAIL>')
        if user:
            print(f"✅ 邮箱查询成功: {user['username']}")
            
            # 测试密码验证
            print("测试密码验证...")
            result = db.verify_password('admin123', user['password_hash'])
            print(f"密码验证结果: {result}")
            
            # 测试完整认证
            print("测试邮箱认证...")
            auth_result = db.authenticate_user('<EMAIL>', 'admin123')
            if auth_result:
                print(f"✅ 邮箱认证成功: {auth_result['username']}")
            else:
                print("❌ 邮箱认证失败")
                
            # 测试用户名认证
            print("测试用户名认证...")
            auth_result2 = db.authenticate_user('admin', 'admin123')
            if auth_result2:
                print(f"✅ 用户名认证成功: {auth_result2['username']}")
            else:
                print("❌ 用户名认证失败")
        else:
            print("❌ 邮箱查询失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_manager()
