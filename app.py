#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QuantTradeX - 完整的量化交易策略平台
支持用户管理、策略开发、回测、实盘交易、社区互动等功能
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
# 可选依赖导入
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    REDIS_AVAILABLE = False
    print("警告: Redis不可用，将使用内存缓存")

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    POSTGRES_AVAILABLE = True
except ImportError:
    psycopg2 = None
    RealDictCursor = None
    POSTGRES_AVAILABLE = False
    print("警告: PostgreSQL不可用，将使用内存存储")
import logging
from functools import wraps
import pyotp
import qrcode
import io
import base64
import threading
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'quanttradex_secret_key_2025_advanced'

# 初始化SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Redis配置
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'quanttradex',
    'user': 'quanttradex_user',
    'password': 'quanttradex123'
}

# ==================== 实时数据流服务类 ====================

class RealTimeDataService:
    """实时数据流服务"""

    def __init__(self, socketio_instance):
        self.socketio = socketio_instance
        self.active_connections = {}  # 活跃连接
        self.subscriptions = {}  # 订阅管理
        self.data_cache = {}  # 数据缓存
        self.cache_timestamps = {}  # 缓存时间戳
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.running = False
        self.update_thread = None

        # 集成统一API
        try:
            from api_config import unified_api
            self.unified_api = unified_api
            logger.info("实时数据服务已集成统一API")
        except ImportError:
            self.unified_api = None
            logger.warning("统一API不可用，将使用备用数据源")

    def start_service(self):
        """启动实时数据服务"""
        if not self.running:
            self.running = True
            self.update_thread = threading.Thread(target=self._data_update_loop, daemon=True)
            self.update_thread.start()
            logger.info("实时数据服务已启动")

    def stop_service(self):
        """停止实时数据服务"""
        self.running = False
        if self.update_thread:
            self.update_thread.join()
        logger.info("实时数据服务已停止")

    def add_connection(self, sid, user_info=None):
        """添加连接"""
        self.active_connections[sid] = {
            'connected_at': datetime.now(),
            'user_info': user_info,
            'subscriptions': set()
        }
        logger.info(f"新连接已添加: {sid}")

    def remove_connection(self, sid):
        """移除连接"""
        if sid in self.active_connections:
            # 清理订阅
            for symbol in self.active_connections[sid]['subscriptions']:
                self.unsubscribe_symbol(sid, symbol)
            del self.active_connections[sid]
            logger.info(f"连接已移除: {sid}")

    def subscribe_symbol(self, sid, symbol, data_type='stock'):
        """订阅股票/数据"""
        if sid not in self.active_connections:
            return False

        subscription_key = f"{data_type}:{symbol}"

        # 添加到连接的订阅列表
        self.active_connections[sid]['subscriptions'].add(subscription_key)

        # 添加到全局订阅管理
        if subscription_key not in self.subscriptions:
            self.subscriptions[subscription_key] = set()
        self.subscriptions[subscription_key].add(sid)

        # 立即发送当前数据
        self._send_current_data(sid, symbol, data_type)

        logger.info(f"订阅添加: {sid} -> {subscription_key}")
        return True

    def unsubscribe_symbol(self, sid, symbol, data_type='stock'):
        """取消订阅"""
        subscription_key = f"{data_type}:{symbol}"

        if sid in self.active_connections:
            self.active_connections[sid]['subscriptions'].discard(subscription_key)

        if subscription_key in self.subscriptions:
            self.subscriptions[subscription_key].discard(sid)
            if not self.subscriptions[subscription_key]:
                del self.subscriptions[subscription_key]

        logger.info(f"订阅移除: {sid} -> {subscription_key}")

    def _send_current_data(self, sid, symbol, data_type):
        """发送当前数据"""
        try:
            if data_type == 'stock':
                data = self._get_stock_realtime_data(symbol)
            elif data_type == 'crypto':
                data = self._get_crypto_realtime_data(symbol)
            else:
                data = None

            if data:
                self.socketio.emit('market_data', {
                    'symbol': symbol,
                    'type': data_type,
                    'data': data,
                    'timestamp': datetime.now().isoformat()
                }, room=sid)
        except Exception as e:
            logger.error(f"发送当前数据失败: {e}")

    def _get_stock_realtime_data(self, symbol):
        """获取股票实时数据"""
        cache_key = f"realtime_stock:{symbol}"
        now = datetime.now()

        # 检查缓存（30秒有效期）
        if (cache_key in self.cache_timestamps and
            (now - self.cache_timestamps[cache_key]).seconds < 30):
            return self.data_cache.get(cache_key)

        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period='1d', interval='1m')

            if not hist.empty:
                latest = hist.iloc[-1]
                data = {
                    'price': float(latest['Close']),
                    'open': float(latest['Open']),
                    'high': float(latest['High']),
                    'low': float(latest['Low']),
                    'volume': int(latest['Volume']),
                    'change': 0,
                    'change_percent': 0,
                    'market_cap': info.get('marketCap', 0),
                    'pe_ratio': info.get('trailingPE', 0),
                    'last_update': now.isoformat()
                }

                # 计算涨跌
                if len(hist) > 1:
                    prev_close = hist.iloc[-2]['Close']
                    data['change'] = float(latest['Close'] - prev_close)
                    data['change_percent'] = float((data['change'] / prev_close) * 100)

                # 缓存数据
                self.data_cache[cache_key] = data
                self.cache_timestamps[cache_key] = now

                return data
        except Exception as e:
            logger.error(f"获取股票实时数据失败 {symbol}: {e}")

        return None

    def _get_crypto_realtime_data(self, symbol):
        """获取加密货币实时数据"""
        cache_key = f"realtime_crypto:{symbol}"
        now = datetime.now()

        # 检查缓存（30秒有效期）
        if (cache_key in self.cache_timestamps and
            (now - self.cache_timestamps[cache_key]).seconds < 30):
            return self.data_cache.get(cache_key)

        try:
            # 使用统一API获取真实数据
            if hasattr(self, 'unified_api') and self.unified_api:
                # 映射符号到CoinGecko ID
                symbol_mapping = {
                    'BTC': 'bitcoin',
                    'ETH': 'ethereum',
                    'BNB': 'binancecoin',
                    'ADA': 'cardano',
                    'SOL': 'solana',
                    'DOT': 'polkadot',
                    'MATIC': 'polygon',
                    'AVAX': 'avalanche-2',
                    'LINK': 'chainlink',
                    'UNI': 'uniswap'
                }

                crypto_id = symbol_mapping.get(symbol.upper(), symbol.lower())
                crypto_data = self.unified_api.get_crypto_data(crypto_id)

                if crypto_data and crypto_data.get('success'):
                    data = crypto_data['data']
                    result = {
                        'price': data.get('price', 0),
                        'change': data.get('change_24h', 0),
                        'change_percent': data.get('change_24h_percent', 0),
                        'volume': data.get('volume_24h', 0),
                        'market_cap': data.get('market_cap', 0),
                        'last_update': datetime.now().isoformat(),
                        'source': 'CoinGecko'
                    }

                    # 缓存数据
                    self.data_cache[cache_key] = result
                    self.cache_timestamps[cache_key] = now
                    return result

            # 如果API不可用，使用CoinGecko直接API
            import requests
            symbol_mapping = {
                'BTC': 'bitcoin',
                'ETH': 'ethereum',
                'BNB': 'binancecoin',
                'ADA': 'cardano',
                'SOL': 'solana'
            }

            crypto_id = symbol_mapping.get(symbol.upper(), 'bitcoin')
            url = f"https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': crypto_id,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true',
                'include_market_cap': 'true'
            }

            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()

            if crypto_id in data:
                crypto_info = data[crypto_id]
                result = {
                    'price': crypto_info.get('usd', 0),
                    'change': crypto_info.get('usd_24h_change', 0),
                    'change_percent': crypto_info.get('usd_24h_change', 0),
                    'volume': crypto_info.get('usd_24h_vol', 0),
                    'market_cap': crypto_info.get('usd_market_cap', 0),
                    'last_update': datetime.now().isoformat(),
                    'source': 'CoinGecko_Direct'
                }

                # 缓存数据
                self.data_cache[cache_key] = result
                self.cache_timestamps[cache_key] = now
                return result

        except Exception as e:
            logger.error(f"获取加密货币实时数据失败 {symbol}: {e}")

        # 如果所有方法都失败，返回模拟数据（但标记为模拟）
        import random
        base_prices = {
            'BTC': 43000,  # 更接近真实价格
            'ETH': 2600,
            'BNB': 300,
            'ADA': 0.5,
            'SOL': 100
        }

        base_price = base_prices.get(symbol.upper(), 1000)

        return {
            'price': base_price + random.uniform(-base_price*0.02, base_price*0.02),
            'change': random.uniform(-base_price*0.05, base_price*0.05),
            'change_percent': random.uniform(-5, 5),
            'volume': random.randint(1000000, 100000000),
            'last_update': datetime.now().isoformat(),
            'source': 'SIMULATED_DATA'  # 标记为模拟数据
        }

    def _data_update_loop(self):
        """数据更新循环"""
        while self.running:
            try:
                # 获取所有需要更新的订阅
                for subscription_key, sids in self.subscriptions.items():
                    if not sids:  # 没有订阅者
                        continue

                    data_type, symbol = subscription_key.split(':', 1)

                    # 获取最新数据
                    if data_type == 'stock':
                        data = self._get_stock_realtime_data(symbol)
                    elif data_type == 'crypto':
                        data = self._get_crypto_realtime_data(symbol)
                    else:
                        continue

                    if data:
                        # 广播给所有订阅者
                        for sid in list(sids):  # 使用list避免运行时修改
                            try:
                                self.socketio.emit('market_data', {
                                    'symbol': symbol,
                                    'type': data_type,
                                    'data': data,
                                    'timestamp': datetime.now().isoformat()
                                }, room=sid)
                            except Exception as e:
                                logger.error(f"发送数据到 {sid} 失败: {e}")
                                # 移除无效连接
                                self.remove_connection(sid)

                # 等待下次更新（根据市场开放时间调整频率）
                time.sleep(5)  # 5秒更新一次

            except Exception as e:
                logger.error(f"数据更新循环错误: {e}")
                time.sleep(10)  # 错误时等待更长时间

    def get_connection_stats(self):
        """获取连接统计"""
        return {
            'total_connections': len(self.active_connections),
            'total_subscriptions': sum(len(subs) for subs in self.subscriptions.values()),
            'unique_symbols': len(self.subscriptions),
            'cache_size': len(self.data_cache)
        }

# ==================== 高级回测引擎类 ====================

class AdvancedBacktestEngine:
    """高级回测引擎"""

    def __init__(self):
        self.commission = 0.001  # 手续费率
        self.slippage = 0.0005   # 滑点
        self.min_trade_amount = 100  # 最小交易金额

    def run_advanced_backtest(self, strategy_config):
        """运行高级回测"""
        try:
            # 获取历史数据
            data = self._get_historical_data(
                strategy_config['symbol'],
                strategy_config['start_date'],
                strategy_config['end_date'],
                strategy_config.get('interval', '1d')
            )

            if data is None or data.empty:
                return {'error': '无法获取历史数据'}

            # 初始化回测环境
            portfolio = self._initialize_portfolio(strategy_config['initial_capital'])

            # 执行策略
            results = self._execute_strategy(data, strategy_config, portfolio)

            # 计算性能指标
            performance = self._calculate_performance_metrics(results, data)

            # 风险分析
            risk_metrics = self._calculate_risk_metrics(results, data)

            # 交易分析
            trade_analysis = self._analyze_trades(results['trades'])

            return {
                'success': True,
                'performance': performance,
                'risk_metrics': risk_metrics,
                'trade_analysis': trade_analysis,
                'equity_curve': results['equity_curve'],
                'trades': results['trades'],
                'drawdown_curve': results['drawdown_curve'],
                'benchmark_comparison': results.get('benchmark_comparison', {}),
                'monthly_returns': results.get('monthly_returns', []),
                'strategy_config': strategy_config
            }

        except Exception as e:
            logger.error(f"高级回测失败: {e}")
            return {'error': str(e)}

    def _get_historical_data(self, symbol, start_date, end_date, interval='1d'):
        """获取历史数据"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(start=start_date, end=end_date, interval=interval)

            if data.empty:
                return None

            # 添加技术指标
            data = self._add_technical_indicators(data)

            return data
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return None

    def _add_technical_indicators(self, data):
        """添加技术指标"""
        try:
            # 移动平均线
            data['SMA_5'] = data['Close'].rolling(window=5).mean()
            data['SMA_10'] = data['Close'].rolling(window=10).mean()
            data['SMA_20'] = data['Close'].rolling(window=20).mean()
            data['SMA_50'] = data['Close'].rolling(window=50).mean()
            data['SMA_200'] = data['Close'].rolling(window=200).mean()

            # 指数移动平均线
            data['EMA_12'] = data['Close'].ewm(span=12).mean()
            data['EMA_26'] = data['Close'].ewm(span=26).mean()

            # MACD
            data['MACD'] = data['EMA_12'] - data['EMA_26']
            data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
            data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']

            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))

            # 布林带
            data['BB_Middle'] = data['Close'].rolling(window=20).mean()
            bb_std = data['Close'].rolling(window=20).std()
            data['BB_Upper'] = data['BB_Middle'] + (bb_std * 2)
            data['BB_Lower'] = data['BB_Middle'] - (bb_std * 2)
            data['BB_Width'] = (data['BB_Upper'] - data['BB_Lower']) / data['BB_Middle']
            data['BB_Position'] = (data['Close'] - data['BB_Lower']) / (data['BB_Upper'] - data['BB_Lower'])

            # ATR (平均真实波幅)
            high_low = data['High'] - data['Low']
            high_close = np.abs(data['High'] - data['Close'].shift())
            low_close = np.abs(data['Low'] - data['Close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            data['ATR'] = true_range.rolling(window=14).mean()

            # 成交量指标
            data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
            data['Volume_Ratio'] = data['Volume'] / data['Volume_SMA']

            return data
        except Exception as e:
            logger.error(f"添加技术指标失败: {e}")
            return data

    def _initialize_portfolio(self, initial_capital):
        """初始化投资组合"""
        return {
            'cash': initial_capital,
            'positions': {},
            'total_value': initial_capital,
            'initial_capital': initial_capital,
            'equity_curve': [initial_capital],
            'trades': [],
            'daily_returns': [],
            'drawdown_curve': [0]
        }

    def _execute_strategy(self, data, strategy_config, portfolio):
        """执行策略"""
        try:
            strategy_type = strategy_config.get('strategy_type', 'buy_and_hold')
            parameters = strategy_config.get('parameters', {})

            if strategy_type == 'moving_average_crossover':
                return self._execute_ma_crossover_strategy(data, parameters, portfolio)
            elif strategy_type == 'rsi_mean_reversion':
                return self._execute_rsi_strategy(data, parameters, portfolio)
            elif strategy_type == 'bollinger_bands':
                return self._execute_bollinger_strategy(data, parameters, portfolio)
            elif strategy_type == 'macd_momentum':
                return self._execute_macd_strategy(data, parameters, portfolio)
            elif strategy_type == 'custom':
                return self._execute_custom_strategy(data, strategy_config, portfolio)
            else:
                return self._execute_buy_and_hold_strategy(data, portfolio)

        except Exception as e:
            logger.error(f"执行策略失败: {e}")
            return portfolio

    def _execute_ma_crossover_strategy(self, data, parameters, portfolio):
        """执行移动平均交叉策略"""
        fast_period = parameters.get('fast_period', 10)
        slow_period = parameters.get('slow_period', 20)
        position_size = parameters.get('position_size', 0.95)  # 使用95%资金

        symbol = 'STOCK'  # 简化处理
        position = 0

        for i in range(slow_period, len(data)):
            current_price = data['Close'].iloc[i]
            fast_ma = data['Close'].iloc[i-fast_period:i].mean()
            slow_ma = data['Close'].iloc[i-slow_period:i].mean()
            prev_fast_ma = data['Close'].iloc[i-fast_period-1:i-1].mean()
            prev_slow_ma = data['Close'].iloc[i-slow_period-1:i-1].mean()

            # 金叉买入信号
            if fast_ma > slow_ma and prev_fast_ma <= prev_slow_ma and position == 0:
                shares_to_buy = int((portfolio['cash'] * position_size) / current_price)
                if shares_to_buy > 0:
                    cost = shares_to_buy * current_price * (1 + self.commission + self.slippage)
                    if cost <= portfolio['cash']:
                        portfolio['cash'] -= cost
                        position = shares_to_buy
                        portfolio['positions'][symbol] = position

                        # 记录交易
                        trade = {
                            'date': data.index[i].strftime('%Y-%m-%d'),
                            'type': 'buy',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'cost': cost,
                            'signal': 'MA_Golden_Cross'
                        }
                        portfolio['trades'].append(trade)

            # 死叉卖出信号
            elif fast_ma < slow_ma and prev_fast_ma >= prev_slow_ma and position > 0:
                proceeds = position * current_price * (1 - self.commission - self.slippage)
                portfolio['cash'] += proceeds

                # 记录交易
                trade = {
                    'date': data.index[i].strftime('%Y-%m-%d'),
                    'type': 'sell',
                    'shares': position,
                    'price': current_price,
                    'proceeds': proceeds,
                    'signal': 'MA_Death_Cross'
                }
                portfolio['trades'].append(trade)

                position = 0
                portfolio['positions'][symbol] = 0

            # 更新投资组合价值
            portfolio_value = portfolio['cash'] + (position * current_price)
            portfolio['equity_curve'].append(portfolio_value)
            portfolio['total_value'] = portfolio_value

            # 计算日收益率
            if len(portfolio['equity_curve']) > 1:
                daily_return = (portfolio_value - portfolio['equity_curve'][-2]) / portfolio['equity_curve'][-2]
                portfolio['daily_returns'].append(daily_return)

            # 计算回撤
            peak = max(portfolio['equity_curve'])
            drawdown = (portfolio_value - peak) / peak
            portfolio['drawdown_curve'].append(drawdown)

        return portfolio

    def _execute_rsi_strategy(self, data, parameters, portfolio):
        """执行RSI均值回归策略"""
        rsi_oversold = parameters.get('rsi_oversold', 30)
        rsi_overbought = parameters.get('rsi_overbought', 70)
        position_size = parameters.get('position_size', 0.95)

        symbol = 'STOCK'
        position = 0

        for i in range(14, len(data)):  # RSI需要14天数据
            current_price = data['Close'].iloc[i]
            rsi = data['RSI'].iloc[i]

            # RSI超卖买入信号
            if rsi < rsi_oversold and position == 0:
                shares_to_buy = int((portfolio['cash'] * position_size) / current_price)
                if shares_to_buy > 0:
                    cost = shares_to_buy * current_price * (1 + self.commission + self.slippage)
                    if cost <= portfolio['cash']:
                        portfolio['cash'] -= cost
                        position = shares_to_buy
                        portfolio['positions'][symbol] = position

                        trade = {
                            'date': data.index[i].strftime('%Y-%m-%d'),
                            'type': 'buy',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'cost': cost,
                            'signal': f'RSI_Oversold_{rsi:.1f}'
                        }
                        portfolio['trades'].append(trade)

            # RSI超买卖出信号
            elif rsi > rsi_overbought and position > 0:
                proceeds = position * current_price * (1 - self.commission - self.slippage)
                portfolio['cash'] += proceeds

                trade = {
                    'date': data.index[i].strftime('%Y-%m-%d'),
                    'type': 'sell',
                    'shares': position,
                    'price': current_price,
                    'proceeds': proceeds,
                    'signal': f'RSI_Overbought_{rsi:.1f}'
                }
                portfolio['trades'].append(trade)

                position = 0
                portfolio['positions'][symbol] = 0

            # 更新投资组合
            portfolio_value = portfolio['cash'] + (position * current_price)
            portfolio['equity_curve'].append(portfolio_value)
            portfolio['total_value'] = portfolio_value

            if len(portfolio['equity_curve']) > 1:
                daily_return = (portfolio_value - portfolio['equity_curve'][-2]) / portfolio['equity_curve'][-2]
                portfolio['daily_returns'].append(daily_return)

            peak = max(portfolio['equity_curve'])
            drawdown = (portfolio_value - peak) / peak
            portfolio['drawdown_curve'].append(drawdown)

        return portfolio

    def _execute_bollinger_strategy(self, data, parameters, portfolio):
        """执行布林带策略"""
        bb_period = parameters.get('bb_period', 20)
        bb_std = parameters.get('bb_std', 2)
        position_size = parameters.get('position_size', 0.95)

        symbol = 'STOCK'
        position = 0

        for i in range(bb_period, len(data)):
            current_price = data['Close'].iloc[i]
            bb_upper = data['BB_Upper'].iloc[i]
            bb_lower = data['BB_Lower'].iloc[i]
            bb_position = data['BB_Position'].iloc[i]

            # 价格触及下轨买入
            if current_price <= bb_lower and position == 0:
                shares_to_buy = int((portfolio['cash'] * position_size) / current_price)
                if shares_to_buy > 0:
                    cost = shares_to_buy * current_price * (1 + self.commission + self.slippage)
                    if cost <= portfolio['cash']:
                        portfolio['cash'] -= cost
                        position = shares_to_buy
                        portfolio['positions'][symbol] = position

                        trade = {
                            'date': data.index[i].strftime('%Y-%m-%d'),
                            'type': 'buy',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'cost': cost,
                            'signal': f'BB_Lower_Touch_{bb_position:.2f}'
                        }
                        portfolio['trades'].append(trade)

            # 价格触及上轨卖出
            elif current_price >= bb_upper and position > 0:
                proceeds = position * current_price * (1 - self.commission - self.slippage)
                portfolio['cash'] += proceeds

                trade = {
                    'date': data.index[i].strftime('%Y-%m-%d'),
                    'type': 'sell',
                    'shares': position,
                    'price': current_price,
                    'proceeds': proceeds,
                    'signal': f'BB_Upper_Touch_{bb_position:.2f}'
                }
                portfolio['trades'].append(trade)

                position = 0
                portfolio['positions'][symbol] = 0

            # 更新投资组合
            portfolio_value = portfolio['cash'] + (position * current_price)
            portfolio['equity_curve'].append(portfolio_value)
            portfolio['total_value'] = portfolio_value

            if len(portfolio['equity_curve']) > 1:
                daily_return = (portfolio_value - portfolio['equity_curve'][-2]) / portfolio['equity_curve'][-2]
                portfolio['daily_returns'].append(daily_return)

            peak = max(portfolio['equity_curve'])
            drawdown = (portfolio_value - peak) / peak
            portfolio['drawdown_curve'].append(drawdown)

        return portfolio

    def _execute_buy_and_hold_strategy(self, data, portfolio):
        """执行买入持有策略"""
        symbol = 'STOCK'
        first_price = data['Close'].iloc[0]

        # 第一天全仓买入
        shares_to_buy = int(portfolio['cash'] / first_price)
        cost = shares_to_buy * first_price * (1 + self.commission)
        portfolio['cash'] -= cost
        portfolio['positions'][symbol] = shares_to_buy

        trade = {
            'date': data.index[0].strftime('%Y-%m-%d'),
            'type': 'buy',
            'shares': shares_to_buy,
            'price': first_price,
            'cost': cost,
            'signal': 'Buy_And_Hold'
        }
        portfolio['trades'].append(trade)

        # 每日更新投资组合价值
        for i in range(len(data)):
            current_price = data['Close'].iloc[i]
            portfolio_value = portfolio['cash'] + (shares_to_buy * current_price)
            portfolio['equity_curve'].append(portfolio_value)
            portfolio['total_value'] = portfolio_value

            if len(portfolio['equity_curve']) > 1:
                daily_return = (portfolio_value - portfolio['equity_curve'][-2]) / portfolio['equity_curve'][-2]
                portfolio['daily_returns'].append(daily_return)

            peak = max(portfolio['equity_curve'])
            drawdown = (portfolio_value - peak) / peak
            portfolio['drawdown_curve'].append(drawdown)

        return portfolio

    def _calculate_performance_metrics(self, portfolio, data):
        """计算性能指标"""
        try:
            initial_capital = portfolio['initial_capital']
            final_value = portfolio['total_value']

            # 基本收益指标
            total_return = (final_value - initial_capital) / initial_capital

            # 年化收益率
            days = len(data)
            years = days / 365.25
            annual_return = (final_value / initial_capital) ** (1 / years) - 1 if years > 0 else 0

            # 日收益率统计
            daily_returns = np.array(portfolio['daily_returns'])
            if len(daily_returns) > 0:
                avg_daily_return = np.mean(daily_returns)
                std_daily_return = np.std(daily_returns)

                # 夏普比率 (假设无风险利率为2%)
                risk_free_rate = 0.02
                excess_return = annual_return - risk_free_rate
                sharpe_ratio = excess_return / (std_daily_return * np.sqrt(252)) if std_daily_return > 0 else 0

                # 索提诺比率
                downside_returns = daily_returns[daily_returns < 0]
                downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0
                sortino_ratio = excess_return / (downside_std * np.sqrt(252)) if downside_std > 0 else 0
            else:
                sharpe_ratio = 0
                sortino_ratio = 0

            # 最大回撤
            max_drawdown = min(portfolio['drawdown_curve']) if portfolio['drawdown_curve'] else 0

            # 胜率
            winning_trades = [t for t in portfolio['trades'] if t['type'] == 'sell' and 'proceeds' in t]
            if len(winning_trades) > 0:
                profits = []
                for i, trade in enumerate(winning_trades):
                    # 找到对应的买入交易
                    buy_trade = None
                    for j in range(i):
                        if portfolio['trades'][j]['type'] == 'buy':
                            buy_trade = portfolio['trades'][j]

                    if buy_trade:
                        profit = trade['proceeds'] - buy_trade['cost']
                        profits.append(profit)

                win_rate = len([p for p in profits if p > 0]) / len(profits) if profits else 0
                avg_win = np.mean([p for p in profits if p > 0]) if profits else 0
                avg_loss = np.mean([p for p in profits if p < 0]) if profits else 0
                profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
            else:
                win_rate = 0
                profit_factor = 0

            return {
                'total_return': total_return * 100,
                'annual_return': annual_return * 100,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'max_drawdown': max_drawdown * 100,
                'win_rate': win_rate * 100,
                'profit_factor': profit_factor,
                'total_trades': len([t for t in portfolio['trades'] if t['type'] == 'buy']),
                'final_value': final_value,
                'initial_capital': initial_capital
            }

        except Exception as e:
            logger.error(f"计算性能指标失败: {e}")
            return {}

    def _calculate_risk_metrics(self, portfolio, data):
        """计算风险指标"""
        try:
            daily_returns = np.array(portfolio['daily_returns'])

            if len(daily_returns) == 0:
                return {}

            # VaR (Value at Risk) - 95%置信度
            var_95 = np.percentile(daily_returns, 5) * 100

            # CVaR (Conditional Value at Risk)
            cvar_95 = np.mean(daily_returns[daily_returns <= np.percentile(daily_returns, 5)]) * 100

            # 波动率
            volatility = np.std(daily_returns) * np.sqrt(252) * 100

            # 偏度和峰度
            from scipy import stats
            skewness = stats.skew(daily_returns)
            kurtosis = stats.kurtosis(daily_returns)

            # 卡尔马比率
            annual_return = ((portfolio['total_value'] / portfolio['initial_capital']) ** (252 / len(daily_returns)) - 1) * 100
            max_drawdown = abs(min(portfolio['drawdown_curve'])) * 100
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0

            return {
                'volatility': volatility,
                'var_95': var_95,
                'cvar_95': cvar_95,
                'skewness': skewness,
                'kurtosis': kurtosis,
                'calmar_ratio': calmar_ratio,
                'max_drawdown_duration': self._calculate_max_drawdown_duration(portfolio['drawdown_curve'])
            }

        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {}

    def _calculate_max_drawdown_duration(self, drawdown_curve):
        """计算最大回撤持续时间"""
        try:
            max_duration = 0
            current_duration = 0

            for dd in drawdown_curve:
                if dd < 0:
                    current_duration += 1
                    max_duration = max(max_duration, current_duration)
                else:
                    current_duration = 0

            return max_duration
        except:
            return 0

    def _analyze_trades(self, trades):
        """分析交易"""
        try:
            if not trades:
                return {}

            buy_trades = [t for t in trades if t['type'] == 'buy']
            sell_trades = [t for t in trades if t['type'] == 'sell']

            # 配对交易分析
            trade_pairs = []
            for sell_trade in sell_trades:
                # 找到最近的买入交易
                buy_trade = None
                for bt in reversed(buy_trades):
                    if bt['date'] <= sell_trade['date']:
                        buy_trade = bt
                        break

                if buy_trade:
                    profit = sell_trade['proceeds'] - buy_trade['cost']
                    holding_days = (pd.to_datetime(sell_trade['date']) - pd.to_datetime(buy_trade['date'])).days

                    trade_pairs.append({
                        'buy_date': buy_trade['date'],
                        'sell_date': sell_trade['date'],
                        'buy_price': buy_trade['price'],
                        'sell_price': sell_trade['price'],
                        'profit': profit,
                        'profit_pct': (profit / buy_trade['cost']) * 100,
                        'holding_days': holding_days
                    })

            if trade_pairs:
                profits = [tp['profit'] for tp in trade_pairs]
                profit_pcts = [tp['profit_pct'] for tp in trade_pairs]
                holding_days = [tp['holding_days'] for tp in trade_pairs]

                winning_trades = [tp for tp in trade_pairs if tp['profit'] > 0]
                losing_trades = [tp for tp in trade_pairs if tp['profit'] < 0]

                return {
                    'total_trades': len(trade_pairs),
                    'winning_trades': len(winning_trades),
                    'losing_trades': len(losing_trades),
                    'win_rate': (len(winning_trades) / len(trade_pairs)) * 100,
                    'avg_profit': np.mean(profits),
                    'avg_profit_pct': np.mean(profit_pcts),
                    'avg_holding_days': np.mean(holding_days),
                    'best_trade': max(profits) if profits else 0,
                    'worst_trade': min(profits) if profits else 0,
                    'avg_win': np.mean([tp['profit'] for tp in winning_trades]) if winning_trades else 0,
                    'avg_loss': np.mean([tp['profit'] for tp in losing_trades]) if losing_trades else 0,
                    'largest_win_pct': max(profit_pcts) if profit_pcts else 0,
                    'largest_loss_pct': min(profit_pcts) if profit_pcts else 0
                }

            return {}

        except Exception as e:
            logger.error(f"分析交易失败: {e}")
            return {}


# ==================== 生成数据加载函数 ====================

def load_generated_forum_posts():
    """加载生成的论坛帖子数据"""
    try:
        # 优先使用最新的论坛数据
        with open('forum_api_data.json', 'r', encoding='utf-8') as f:
            forum_data = json.load(f)
            return forum_data.get('posts', [])
    except:
        try:
            # 回退到完整内容
            with open('generated_data/forum_posts_complete.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            try:
                # 回退到原始生成数据
                with open('generated_data/forum_posts.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return mock_forum_posts  # 最后回退到原始数据

def load_strategy_code(strategy_id):
    """加载策略代码"""
    try:
        with open('strategy_codes_mapping.json', 'r', encoding='utf-8') as f:
            codes = json.load(f)
            strategy_data = codes.get(str(strategy_id))
            if strategy_data and isinstance(strategy_data, dict):
                return strategy_data.get('code', "# 策略代码未找到\npass")
            else:
                return strategy_data or "# 策略代码未找到\npass"
    except:
        return "# 策略代码加载失败\npass"

def load_strategy_info(strategy_id):
    """加载策略信息"""
    try:
        with open('strategy_codes_mapping.json', 'r', encoding='utf-8') as f:
            codes = json.load(f)
            strategy_data = codes.get(str(strategy_id))
            if strategy_data and isinstance(strategy_data, dict):
                return strategy_data
            else:
                return {"name": f"策略{strategy_id}", "code": strategy_data or "# 策略代码未找到\npass"}
    except:
        return {"name": f"策略{strategy_id}", "code": "# 策略代码加载失败\npass"}

def load_generated_comments():
    """加载生成的用户评论"""
    try:
        with open('generated_data/user_comments.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return []

# ==================== 核心服务类 ====================

class QuantTradeXService:
    def __init__(self):
        self.redis_client = None
        self.db_connection = None
        self.init_connections()

    def init_connections(self):
        """初始化数据库连接"""
        try:
            # Redis连接
            self.redis_client = redis.Redis(**REDIS_CONFIG)
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")

        try:
            # PostgreSQL连接
            self.db_connection = psycopg2.connect(**DB_CONFIG)
            logger.info("PostgreSQL连接成功")
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")

    def get_stock_data(self, symbol, period='1mo'):
        """获取股票数据"""
        try:
            # 先从缓存获取
            cache_key = f"stock_data:{symbol}:{period}"
            if self.redis_client:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)

            # 从Yahoo Finance获取数据
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)

            if hist.empty:
                return None

            # 转换为JSON格式
            data = {
                'symbol': symbol,
                'dates': hist.index.strftime('%Y-%m-%d').tolist(),
                'open': hist['Open'].tolist(),
                'high': hist['High'].tolist(),
                'low': hist['Low'].tolist(),
                'close': hist['Close'].tolist(),
                'volume': hist['Volume'].tolist(),
                'last_update': datetime.now().isoformat()
            }

            # 缓存数据（5分钟）
            if self.redis_client:
                self.redis_client.setex(cache_key, 300, json.dumps(data))

            return data
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return None

    def calculate_indicators(self, data):
        """计算技术指标"""
        try:
            if not data or len(data['close']) < 20:
                return {}

            closes = np.array(data['close'])

            # 简单移动平均线
            sma_20 = np.convolve(closes, np.ones(20)/20, mode='valid')
            sma_50 = np.convolve(closes, np.ones(50)/50, mode='valid') if len(closes) >= 50 else []

            # RSI计算
            def calculate_rsi(prices, period=14):
                deltas = np.diff(prices)
                gains = np.where(deltas > 0, deltas, 0)
                losses = np.where(deltas < 0, -deltas, 0)

                avg_gains = np.convolve(gains, np.ones(period)/period, mode='valid')
                avg_losses = np.convolve(losses, np.ones(period)/period, mode='valid')

                rs = avg_gains / (avg_losses + 1e-10)
                rsi = 100 - (100 / (1 + rs))
                return rsi

            rsi = calculate_rsi(closes) if len(closes) > 14 else []

            return {
                'sma_20': sma_20.tolist(),
                'sma_50': sma_50.tolist(),
                'rsi': rsi.tolist(),
                'current_price': closes[-1],
                'price_change': closes[-1] - closes[-2] if len(closes) > 1 else 0,
                'price_change_pct': ((closes[-1] - closes[-2]) / closes[-2] * 100) if len(closes) > 1 else 0
            }
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}

    def run_backtest(self, strategy_code, symbol, start_date, end_date, initial_capital=100000, parameters=None):
        """运行回测"""
        try:
            # 获取历史数据
            ticker = yf.Ticker(symbol)
            hist = ticker.history(start=start_date, end=end_date)

            if hist.empty:
                return {'error': '无法获取历史数据'}

            # 这里应该实现策略执行逻辑
            # 简化版本：计算买入持有策略的收益
            initial_price = hist['Close'].iloc[0]
            final_price = hist['Close'].iloc[-1]
            total_return = (final_price - initial_price) / initial_price * 100

            results = {
                'total_return': total_return,
                'final_value': initial_capital * (1 + total_return / 100),
                'max_drawdown': 0,  # 简化
                'sharpe_ratio': 0,  # 简化
                'trades_count': 1,
                'win_rate': 100 if total_return > 0 else 0,
                'start_date': start_date,
                'end_date': end_date,
                'symbol': symbol
            }

            return results
        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {'error': str(e)}

# 导入API配置
try:
    from api_config import unified_api, DataProviderManager
    api_provider_manager = DataProviderManager()
    logger.info("API配置模块加载成功")
except ImportError as e:
    logger.warning(f"API配置模块加载失败: {e}")
    unified_api = None
    api_provider_manager = None

# 导入支付服务
try:
    from payment_service import payment_service
    logger.info("支付服务模块加载成功")
except ImportError as e:
    logger.warning(f"支付服务模块加载失败: {e}")
    payment_service = None

# 导入数据库管理器
try:
    from database_manager import get_db_manager
    db_manager = get_db_manager()
    logger.info("数据库管理器模块加载成功")
except ImportError as e:
    logger.warning(f"数据库管理器模块加载失败: {e}")
    db_manager = None

# 导入并注册蓝图
try:
    from routes.auth import auth_bp
    from routes.main import main_bp
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    logger.info("蓝图注册成功")
except ImportError as e:
    logger.warning(f"蓝图导入失败: {e}")
    # 如果蓝图导入失败，继续使用现有的路由

# 创建服务实例
quanttradex_service = QuantTradeXService()
realtime_service = RealTimeDataService(socketio)
backtest_engine = AdvancedBacktestEngine()

# ==================== 模拟数据库 ====================

# 模拟用户数据
MOCK_USERS = {
    'admin': {
        'id': 1,
        'username': 'admin',
        'email': '<EMAIL>',
        'password': 'admin123',
        'full_name': '系统管理员',
        'role': 'admin',
        'avatar_url': '/static/img/avatar-admin.png',
        'bio': 'QuantTradeX平台管理员，负责平台运营和技术支持',
        'phone': '+86 138****0001',
        'region': 'cn',
        'risk_preference': 'moderate',
        'experience': 'expert',
        'created_at': '2025-01-01T00:00:00',
        'updated_at': '2025-01-01T00:00:00',
        'last_login': '2025-01-27T01:00:00',
        'is_premium': True,
        'premium_expires': '2026-01-01T00:00:00',
        'total_strategies': 5,
        'total_backtests': 25,
        'total_profit': 156780.50,
        'win_rate': 78.5,
        'followers': 1250,
        'following': 45,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'demo_user': {
        'id': 2,
        'username': 'demo_user',
        'email': '<EMAIL>',
        'password': 'demo123',
        'full_name': '演示用户',
        'role': 'user',
        'avatar_url': '/static/img/avatar-demo.png',
        'bio': '量化交易新手，正在学习各种策略和技术分析',
        'phone': '+86 138****0002',
        'region': 'cn',
        'risk_preference': 'conservative',
        'experience': 'beginner',
        'created_at': '2025-01-10T10:00:00',
        'updated_at': '2025-01-10T10:00:00',
        'last_login': '2025-01-26T18:30:00',
        'is_premium': False,
        'premium_expires': None,
        'total_strategies': 2,
        'total_backtests': 8,
        'total_profit': 2340.80,
        'win_rate': 62.5,
        'followers': 23,
        'following': 156,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'trader_pro': {
        'id': 3,
        'username': 'trader_pro',
        'email': '<EMAIL>',
        'password': 'trader123',
        'full_name': '专业交易员',
        'role': 'premium',
        'avatar_url': '/static/img/avatar-pro.png',
        'bio': '10年量化交易经验，专注高频策略开发，年化收益率25%+',
        'phone': '+86 138****0003',
        'region': 'hk',
        'risk_preference': 'aggressive',
        'experience': 'expert',
        'created_at': '2025-01-05T15:30:00',
        'updated_at': '2025-01-05T15:30:00',
        'last_login': '2025-01-27T00:45:00',
        'is_premium': True,
        'premium_expires': '2025-12-05T15:30:00',
        'total_strategies': 12,
        'total_backtests': 156,
        'total_profit': 89650.25,
        'win_rate': 85.2,
        'followers': 892,
        'following': 78,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'quant_master': {
        'id': 4,
        'username': 'quant_master',
        'email': '<EMAIL>',
        'password': 'quant123',
        'full_name': '量化大师',
        'role': 'premium',
        'avatar_url': '/static/img/avatar-master.png',
        'bio': '金融工程博士，专注机器学习在量化交易中的应用',
        'phone': '+86 138****0004',
        'region': 'us',
        'risk_preference': 'moderate',
        'experience': 'expert',
        'created_at': '2024-12-15T09:20:00',
        'updated_at': '2024-12-15T09:20:00',
        'last_login': '2025-01-26T22:15:00',
        'is_premium': True,
        'premium_expires': '2025-12-15T09:20:00',
        'total_strategies': 18,
        'total_backtests': 234,
        'total_profit': 234567.89,
        'win_rate': 82.7,
        'followers': 1567,
        'following': 234,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'algo_trader': {
        'id': 5,
        'username': 'algo_trader',
        'email': '<EMAIL>',
        'password': 'algo123',
        'full_name': '算法交易者',
        'role': 'user',
        'avatar_url': '/static/img/avatar-algo.png',
        'bio': '程序员转行做量化，擅长策略自动化和系统优化',
        'phone': '+86 138****0005',
        'region': 'cn',
        'risk_preference': 'moderate',
        'experience': 'intermediate',
        'created_at': '2025-01-08T14:45:00',
        'updated_at': '2025-01-08T14:45:00',
        'last_login': '2025-01-26T16:20:00',
        'is_premium': False,
        'premium_expires': None,
        'total_strategies': 6,
        'total_backtests': 45,
        'total_profit': 12450.60,
        'win_rate': 71.3,
        'followers': 156,
        'following': 89,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'crypto_king': {
        'id': 6,
        'username': 'crypto_king',
        'email': '<EMAIL>',
        'password': 'crypto123',
        'full_name': '数字货币之王',
        'role': 'premium',
        'avatar_url': '/static/img/avatar-crypto.png',
        'bio': '专注数字货币量化交易，24小时不间断策略运行',
        'phone': '+86 138****0006',
        'region': 'tw',
        'risk_preference': 'aggressive',
        'experience': 'advanced',
        'created_at': '2024-11-20T11:30:00',
        'updated_at': '2024-11-20T11:30:00',
        'last_login': '2025-01-27T01:10:00',
        'is_premium': True,
        'premium_expires': '2025-11-20T11:30:00',
        'total_strategies': 15,
        'total_backtests': 189,
        'total_profit': 567890.12,
        'win_rate': 79.8,
        'followers': 2134,
        'following': 67,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'risk_manager': {
        'id': 7,
        'username': 'risk_manager',
        'email': '<EMAIL>',
        'password': 'risk123',
        'full_name': '风险管理专家',
        'role': 'user',
        'avatar_url': '/static/img/avatar-risk.png',
        'bio': '专注风险控制和资金管理，追求稳健收益',
        'phone': '+86 138****0007',
        'region': 'cn',
        'risk_preference': 'conservative',
        'experience': 'advanced',
        'created_at': '2025-01-12T08:15:00',
        'updated_at': '2025-01-12T08:15:00',
        'last_login': '2025-01-26T20:45:00',
        'is_premium': False,
        'premium_expires': None,
        'total_strategies': 4,
        'total_backtests': 28,
        'total_profit': 8765.43,
        'win_rate': 88.9,
        'followers': 345,
        'following': 123,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    },
    'newbie_trader': {
        'id': 8,
        'username': 'newbie_trader',
        'email': '<EMAIL>',
        'password': 'newbie123',
        'full_name': '新手交易员',
        'role': 'user',
        'avatar_url': '/static/img/avatar-newbie.png',
        'bio': '刚入门的量化交易学习者，希望向大家学习',
        'phone': '+86 138****0008',
        'region': 'cn',
        'risk_preference': 'conservative',
        'experience': 'beginner',
        'created_at': '2025-01-20T16:30:00',
        'updated_at': '2025-01-20T16:30:00',
        'last_login': '2025-01-26T19:00:00',
        'is_premium': False,
        'premium_expires': None,
        'total_strategies': 1,
        'total_backtests': 3,
        'total_profit': 123.45,
        'win_rate': 33.3,
        'followers': 5,
        'following': 234,
        'two_factor_enabled': False,
        'two_factor_secret': None,
        'backup_codes': []
    }
}

# 当前登录用户
current_user_session = {}

# ==================== 路由处理 ====================

# 主页和基础页面
@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """交易仪表板"""
    return render_template('dashboard.html')

@app.route('/strategies')
def strategies():
    """策略市场"""
    return render_template('strategies.html')

@app.route('/my-strategies')
def my_strategies():
    """我的策略"""
    return render_template('my-strategies.html')

@app.route('/backtest')
def backtest():
    """回测页面"""
    return render_template('backtest.html')

@app.route('/forum')
def forum():
    """论坛页面"""
    return render_template('forum.html')

@app.route('/strategy-editor')
def strategy_editor():
    """策略开发页面"""
    return render_template('strategy_editor.html')

@app.route('/security')
def security():
    """安全设置页面"""
    return render_template('security.html')

@app.route('/realtime')
def realtime():
    """实时数据流页面"""
    return render_template('realtime.html')

@app.route('/realtime_test')
def realtime_test():
    """实时数据流测试页面"""
    return render_template('realtime_test.html')

@app.route('/advanced_backtest')
def advanced_backtest():
    """高级回测页面"""
    return render_template('advanced_backtest.html')

@app.route('/profile')
def profile_page():
    """个人资料页面"""
    return render_template('profile.html')

@app.route('/datacenter')
def datacenter():
    """数据中心页面"""
    return render_template('datacenter.html')

@app.route('/demo')
def feature_demo():
    """新功能演示页面"""
    return render_template('feature_demo.html')

@app.route('/api/realtime/crypto/<symbol>')
def get_crypto_realtime(symbol):
    """获取加密货币实时数据API"""
    try:
        # 使用实时数据服务获取数据
        if realtime_service:
            data = realtime_service._get_crypto_realtime_data(symbol.upper())
            if data:
                return jsonify(data)

        # 如果实时服务不可用，直接调用CoinGecko API
        import requests
        symbol_mapping = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum',
            'BNB': 'binancecoin',
            'ADA': 'cardano',
            'SOL': 'solana'
        }

        crypto_id = symbol_mapping.get(symbol.upper(), 'bitcoin')
        url = f"https://api.coingecko.com/api/v3/simple/price"
        params = {
            'ids': crypto_id,
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true',
            'include_market_cap': 'true'
        }

        response = requests.get(url, params=params, timeout=5)
        response.raise_for_status()
        api_data = response.json()

        if crypto_id in api_data:
            crypto_info = api_data[crypto_id]
            result = {
                'price': crypto_info.get('usd', 0),
                'change': crypto_info.get('usd_24h_change', 0),
                'change_percent': crypto_info.get('usd_24h_change', 0),
                'volume': crypto_info.get('usd_24h_vol', 0),
                'market_cap': crypto_info.get('usd_market_cap', 0),
                'last_update': datetime.now().isoformat(),
                'source': 'CoinGecko_API'
            }
            return jsonify(result)

        return jsonify({'error': '未找到该加密货币数据'}), 404

    except Exception as e:
        logger.error(f"获取加密货币实时数据失败: {e}")
        return jsonify({'error': '获取数据失败'}), 500

@app.route('/api/realtime/stock/<symbol>')
def get_stock_realtime(symbol):
    """获取股票实时数据API"""
    try:
        # 使用实时数据服务获取数据
        if realtime_service:
            data = realtime_service._get_stock_realtime_data(symbol.upper())
            if data:
                return jsonify(data)

        return jsonify({'error': '获取股票数据失败'}), 500

    except Exception as e:
        logger.error(f"获取股票实时数据失败: {e}")
        return jsonify({'error': '获取数据失败'}), 500

# ==================== 认证相关路由 ====================

@app.route('/auth/login', methods=['POST'])
def login():
    """用户登录 - 支持用户名或邮箱登录"""
    try:
        data = request.get_json()
        login_identifier = data.get('username') or data.get('email')  # 支持用户名或邮箱字段
        password = data.get('password')

        if not login_identifier or not password:
            return jsonify({'success': False, 'error': '用户名/邮箱和密码不能为空'})

        # 使用数据库管理器进行用户认证
        if db_manager:
            try:
                user = db_manager.authenticate_user(login_identifier, password)

                if not user:
                    return jsonify({'success': False, 'error': '用户名/邮箱或密码错误'})

                # 登录成功，保存到session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role'] = user['role']
                session['is_premium'] = user['is_premium']
                session['premium_expires'] = user.get('premium_expires')

                # 返回用户信息
                user_info = user
            except Exception as e:
                logger.error(f"数据库认证失败: {e}")
                # 回退到MOCK_USERS
                user = None
                username = None

                # 首先尝试用户名登录
                user = MOCK_USERS.get(login_identifier)
                if user:
                    username = login_identifier
                else:
                    # 如果用户名不存在，尝试邮箱登录
                    for uname, udata in MOCK_USERS.items():
                        if udata.get('email') == login_identifier:
                            user = udata
                            username = uname
                            break

                if not user or user.get('password') != password:
                    return jsonify({'success': False, 'error': '用户名/邮箱或密码错误'})

                # 登录成功，保存到session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role'] = user['role']
                session['is_premium'] = user.get('is_premium', False)
                session['premium_expires'] = user.get('premium_expires')

                # 返回用户信息（不包含密码）
                user_info = {k: v for k, v in user.items() if k != 'password'}
        else:
            # 回退到MOCK_USERS（兼容性）
            # 首先尝试用户名登录
            user = MOCK_USERS.get(login_identifier)
            if user:
                username = login_identifier
            else:
                # 如果用户名不存在，尝试邮箱登录
                for uname, udata in MOCK_USERS.items():
                    if udata.get('email') == login_identifier:
                        user = udata
                        username = uname
                        break

            if not user:
                return jsonify({'success': False, 'error': '用户名/邮箱或密码错误'})

            # 验证密码
            if user['password'] != password:
                return jsonify({'success': False, 'error': '用户名/邮箱或密码错误'})

            # 更新最后登录时间
            user['last_login'] = datetime.now().isoformat()

            # 登录成功，保存到session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            session['is_premium'] = user['is_premium']
            session['premium_expires'] = user.get('premium_expires')

            # 返回用户信息（不包含密码）
            user_info = {k: v for k, v in user.items() if k != 'password'}

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': user_info
        })

    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'error': '登录失败，请稍后重试'})

@app.route('/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        full_name = data.get('full_name', '')

        if not username or not email or not password:
            return jsonify({'success': False, 'error': '用户名、邮箱和密码不能为空'})

        # 使用数据库管理器
        if db_manager:
            try:
                new_user = db_manager.create_user(
                    username=username,
                    email=email,
                    password=password,
                    full_name=full_name
                )

                # 自动登录新用户
                session['user_id'] = new_user['id']
                session['username'] = new_user['username']
                session['role'] = new_user['role']
                session['is_premium'] = new_user['is_premium']

                return jsonify({
                    'success': True,
                    'message': '注册成功！',
                    'user': new_user,
                    'redirect_url': '/dashboard'
                })
            except ValueError as e:
                return jsonify({'success': False, 'error': str(e)})
            except Exception as e:
                logger.error(f"注册失败: {e}")
                return jsonify({'success': False, 'error': '注册失败，请稍后重试'})
        else:
            # 回退到MOCK_USERS（兼容性）
            # 检查用户名是否已存在
            if username in MOCK_USERS:
                return jsonify({'success': False, 'error': '用户名已存在'})

            # 检查邮箱是否已存在
            for user in MOCK_USERS.values():
                if user['email'] == email:
                    return jsonify({'success': False, 'error': '邮箱已被注册'})

            # 创建新用户
            new_user = {
                'id': len(MOCK_USERS) + 1,
                'username': username,
                'email': email,
                'password': password,
                'full_name': full_name,
                'role': 'user',
                'avatar_url': '/static/img/avatar-default.png',
                'bio': '',
                'created_at': datetime.now().isoformat(),
                'is_premium': False
            }

            # 保存用户（在实际应用中应该保存到数据库）
            MOCK_USERS[username] = new_user

            return jsonify({
                'success': True,
                'message': '注册成功，请登录',
                'user': {k: v for k, v in new_user.items() if k != 'password'}
            })

    except Exception as e:
        logger.error(f"注册失败: {e}")
        return jsonify({'success': False, 'error': '注册失败，请稍后重试'})

@app.route('/auth/logout', methods=['POST'])
def logout():
    """用户登出"""
    session.clear()
    return jsonify({'success': True, 'message': '已退出登录'})

@app.route('/auth/status')
def auth_status():
    """获取用户登录状态"""
    if 'username' not in session:
        return jsonify({'success': False, 'user': None})

    username = session['username']

    # 使用数据库管理器
    if db_manager:
        user = db_manager.get_user_by_username(username)
        if not user:
            return jsonify({'success': False, 'user': None})

        # 移除密码字段
        user_info = {k: v for k, v in user.items() if k != 'password_hash'}
        return jsonify({'success': True, 'user': user_info})
    else:
        # 回退到MOCK_USERS
        user = MOCK_USERS.get(username)
        if not user:
            return jsonify({'success': False, 'user': None})

        user_info = {k: v for k, v in user.items() if k != 'password'}
        return jsonify({'success': True, 'user': user_info})

@app.route('/api/user/status')
def api_user_status():
    """API用户状态检查接口"""
    if 'username' not in session:
        return jsonify({'logged_in': False, 'user': None})

    username = session['username']

    # 使用数据库管理器
    if db_manager:
        user = db_manager.get_user_by_username(username)
        if not user:
            return jsonify({'logged_in': False, 'user': None})

        # 移除密码字段
        user_info = {k: v for k, v in user.items() if k != 'password_hash'}
        return jsonify({'logged_in': True, 'user': user_info})
    else:
        # 回退到MOCK_USERS
        user = MOCK_USERS.get(username)
        if not user:
            return jsonify({'logged_in': False, 'user': None})

        user_info = {k: v for k, v in user.items() if k != 'password'}
        return jsonify({'logged_in': True, 'user': user_info})

@app.route('/api/auth/login', methods=['POST'])
def api_login():
    """API登录接口"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        remember_me = data.get('remember_me', False)

        if not email or not password:
            return jsonify({'success': False, 'message': '邮箱和密码不能为空'})

        # 查找邮箱对应的用户
        user = None
        username = None

        # 使用数据库管理器
        if db_manager:
            user = db_manager.authenticate_user(email, password)
            if user:
                username = user['username']
        else:
            # 回退到MOCK_USERS
            for uname, udata in MOCK_USERS.items():
                if udata.get('email') == email and udata.get('password') == password:
                    user = udata
                    username = uname
                    break

        if user and username:
            # 设置会话
            session.permanent = remember_me
            session['user_id'] = user['id']
            session['username'] = username
            session['role'] = user['role']
            session['is_premium'] = user.get('is_premium', False)
            session['full_name'] = user['full_name']
            session['avatar_url'] = user.get('avatar_url', '')

            # 移除敏感信息
            safe_user = {k: v for k, v in user.items() if k != 'password'}

            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': safe_user
            })
        else:
            return jsonify({
                'success': False,
                'message': '邮箱或密码错误'
            })

    except Exception as e:
        logger.error(f"API登录失败: {e}")
        return jsonify({'success': False, 'message': '登录服务异常'})

@app.route('/api/auth/register', methods=['POST'])
def api_register():
    """API注册接口"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field} 不能为空'})

        # 验证密码长度
        if len(data['password']) < 6:
            return jsonify({'success': False, 'message': '密码长度至少6位'})

        username = data['username']
        email = data['email']
        password = data['password']

        # 检查用户名和邮箱是否已存在
        if db_manager:
            if db_manager.get_user_by_username(username):
                return jsonify({'success': False, 'message': '用户名已存在'})

            if db_manager.get_user_by_email(email):
                return jsonify({'success': False, 'message': '邮箱已被注册'})
        else:
            if username in MOCK_USERS:
                return jsonify({'success': False, 'message': '用户名已存在'})

            for udata in MOCK_USERS.values():
                if udata.get('email') == email:
                    return jsonify({'success': False, 'message': '邮箱已被注册'})

        # 创建新用户
        new_user = {
            'id': len(MOCK_USERS) + 1,
            'username': username,
            'email': email,
            'password': password,
            'full_name': data.get('full_name', username),
            'role': 'user',
            'avatar_url': '/static/img/avatar-default.png',
            'bio': '',
            'phone': '',
            'region': data.get('region', 'cn'),
            'risk_preference': data.get('risk_preference', 'moderate'),
            'experience': data.get('experience', 'beginner'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'last_login': datetime.now().isoformat(),
            'is_premium': False,
            'premium_expires': None,
            'total_strategies': 0,
            'total_backtests': 0,
            'total_profit': 0,
            'win_rate': 0,
            'followers': 0,
            'following': 0,
            'two_factor_enabled': False,
            'two_factor_secret': None,
            'backup_codes': []
        }

        # 保存用户
        if db_manager:
            try:
                created_user = db_manager.create_user(
                    username=username,
                    email=email,
                    password=password,
                    full_name=new_user.get('full_name', username)
                )
                new_user.update(created_user)
            except ValueError as e:
                return jsonify({'success': False, 'message': str(e)})
        else:
            MOCK_USERS[username] = new_user

        # 自动登录
        session.permanent = True
        session['user_id'] = new_user['id']
        session['username'] = username
        session['role'] = new_user['role']
        session['is_premium'] = False
        session['full_name'] = new_user['full_name']

        # 移除敏感信息
        safe_user = {k: v for k, v in new_user.items() if k != 'password'}

        return jsonify({
            'success': True,
            'message': '注册成功',
            'user': safe_user
        })

    except Exception as e:
        logger.error(f"API注册失败: {e}")
        return jsonify({'success': False, 'message': '注册服务异常'})

@app.route('/api/auth/logout', methods=['POST'])
def api_logout():
    """API退出登录接口"""
    try:
        # 清除会话
        session.clear()
        return jsonify({
            'success': True,
            'message': '已安全退出'
        })
    except Exception as e:
        logger.error(f"API退出登录失败: {e}")
        return jsonify({'success': False, 'message': '退出失败'})

@app.route('/auth/profile', methods=['GET', 'POST'])
def profile():
    """获取或更新用户资料"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    username = session['username']

    # 使用数据库管理器
    if db_manager:
        user = db_manager.get_user_by_username(username)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        if request.method == 'GET':
            # 获取用户资料
            user_info = {k: v for k, v in user.items() if k != 'password_hash'}
            return jsonify({'success': True, 'user': user_info})
    else:
        # 回退到MOCK_USERS
        user = MOCK_USERS.get(username)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        if request.method == 'GET':
            # 获取用户资料
            user_info = {k: v for k, v in user.items() if k != 'password'}
            return jsonify({'success': True, 'user': user_info})

        elif request.method == 'POST':
            # 使用数据库用户管理器更新用户资料
            try:
                data = request.get_json()

                # 验证必填字段
                if not data.get('email'):
                    return jsonify({'success': False, 'error': '邮箱不能为空'})

                # 更新用户信息
                success, message = user_manager.update_user(username, **data)

                if success:
                    # 获取更新后的用户信息
                    updated_user = user_manager.get_user_by_username(username)
                    user_info = {k: v for k, v in updated_user.items() if k != 'password'}
                    return jsonify({
                        'success': True,
                        'message': message,
                        'user': user_info
                    })
                else:
                    return jsonify({'success': False, 'error': message})

            except Exception as e:
                logger.error(f"更新用户资料失败: {e}")
                return jsonify({'success': False, 'error': '更新失败，请稍后重试'})

        elif request.method == 'POST':
            # 更新用户资料
            try:
                data = request.get_json()

                # 验证必填字段
                if not data.get('username') or not data.get('email'):
                    return jsonify({'success': False, 'error': '用户名和邮箱不能为空'})

                # 检查用户名是否被其他用户占用
                new_username = data.get('username')
                if new_username != username:
                    if new_username in MOCK_USERS:
                        return jsonify({'success': False, 'error': '用户名已被占用'})

                # 检查邮箱是否被其他用户占用
                new_email = data.get('email')
                for other_username, other_user in MOCK_USERS.items():
                    if other_username != username and other_user['email'] == new_email:
                        return jsonify({'success': False, 'error': '邮箱已被其他用户使用'})

                # 更新用户信息
                user['full_name'] = data.get('full_name', user.get('full_name', ''))
                user['email'] = new_email
                user['bio'] = data.get('bio', user.get('bio', ''))
                user['phone'] = data.get('phone', user.get('phone', ''))
                user['region'] = data.get('region', user.get('region', ''))
                user['risk_preference'] = data.get('risk_preference', user.get('risk_preference', ''))
                user['experience'] = data.get('experience', user.get('experience', ''))
                user['updated_at'] = datetime.now().isoformat()

                # 如果用户名发生变化，需要更新session和MOCK_USERS字典
                if new_username != username:
                    # 更新用户名
                    user['username'] = new_username
                    # 在字典中移动用户数据
                    MOCK_USERS[new_username] = user
                    del MOCK_USERS[username]
                    # 更新session
                    session['username'] = new_username

                # 返回更新后的用户信息
                user_info = {k: v for k, v in user.items() if k != 'password'}
                return jsonify({
                    'success': True,
                    'message': '个人资料更新成功',
                    'user': user_info
                })

            except Exception as e:
                logger.error(f"更新用户资料失败: {e}")
                return jsonify({'success': False, 'error': '更新失败，请稍后重试'})

@app.route('/auth/upgrade', methods=['POST'])
def upgrade_premium():
    """升级VIP会员 - 创建支付订单"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        plan = data.get('plan', 'monthly')  # monthly, yearly, lifetime
        payment_method = data.get('payment_method', 'mock')  # mock, alipay, wechat, paypal, stripe

        username = session['username']
        user = MOCK_USERS.get(username)

        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        if not payment_service:
            return jsonify({'success': False, 'error': '支付服务未启用'})

        # 创建支付订单
        order_result = payment_service.create_order(
            user_id=user['id'],
            plan_type=plan,
            payment_method=payment_method
        )

        if not order_result['success']:
            return jsonify(order_result)

        # 保存订单信息到session，用于支付完成后的处理
        session['pending_upgrade'] = {
            'order_id': order_result['order_id'],
            'plan': plan,
            'user_id': user['id']
        }

        return jsonify({
            'success': True,
            'message': '支付订单已创建，请完成支付',
            'order_id': order_result['order_id'],
            'payment_url': order_result['payment_url'],
            'amount': order_result['amount'],
            'currency': order_result['currency'],
            'expires_at': order_result['expires_at'],
            'payment_method': payment_method
        })

    except Exception as e:
        logger.error(f"创建升级订单失败: {e}")
        return jsonify({'success': False, 'error': '创建订单失败，请稍后重试'})

@app.route('/auth/upgrade/complete/<order_id>')
def complete_upgrade(order_id):
    """完成VIP升级"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        if not payment_service:
            return jsonify({'success': False, 'error': '支付服务未启用'})

        # 获取订单信息
        order = payment_service.get_order(order_id)
        if not order:
            return jsonify({'success': False, 'error': '订单不存在'})

        if order.status != 'paid':
            return jsonify({'success': False, 'error': '订单未支付'})

        # 获取用户信息
        username = session['username']
        user = MOCK_USERS.get(username)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        # 确定会员期限
        plan_config = {
            'monthly': {'days': 30, 'name': '月度会员'},
            'yearly': {'days': 365, 'name': '年度会员'},
            'lifetime': {'days': 36500, 'name': '终身会员'}
        }

        pending_upgrade = session.get('pending_upgrade', {})
        plan = pending_upgrade.get('plan', 'monthly')

        if plan not in plan_config:
            return jsonify({'success': False, 'error': '无效的会员计划'})

        config = plan_config[plan]

        # 计算到期时间
        if user.get('is_premium') and user.get('premium_expires'):
            # 如果已经是VIP，在现有基础上延长
            current_expires = datetime.fromisoformat(user['premium_expires'])
            if current_expires > datetime.now():
                expires = current_expires + timedelta(days=config['days'])
            else:
                expires = datetime.now() + timedelta(days=config['days'])
        else:
            expires = datetime.now() + timedelta(days=config['days'])

        # 更新用户会员状态
        user['is_premium'] = True
        user['role'] = 'premium'
        user['premium_expires'] = expires.isoformat()
        user['last_payment_order'] = order_id
        user['last_payment_at'] = datetime.now().isoformat()

        # 更新session
        session['is_premium'] = True
        session['role'] = 'premium'
        session['premium_expires'] = expires.isoformat()

        # 清除待处理的升级信息
        session.pop('pending_upgrade', None)

        return jsonify({
            'success': True,
            'message': f'恭喜！您已成功升级为{config["name"]}',
            'plan': plan,
            'expires': expires.isoformat(),
            'order_id': order_id,
            'transaction_id': order.transaction_id,
            'user': {k: v for k, v in user.items() if k != 'password'}
        })

    except Exception as e:
        logger.error(f"完成VIP升级失败: {e}")
        return jsonify({'success': False, 'error': '升级失败，请稍后重试'})

@app.route('/auth/users')
def get_users():
    """获取用户列表（管理员功能）"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    if session.get('role') != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    # 返回所有用户信息（不包含密码）
    users = []
    for user in MOCK_USERS.values():
        user_info = {k: v for k, v in user.items() if k != 'password'}
        users.append(user_info)

    return jsonify({
        'success': True,
        'users': users,
        'total': len(users)
    })

@app.route('/auth/user/<username>')
def get_user_profile(username):
    """获取用户公开资料"""
    user = MOCK_USERS.get(username)

    if not user:
        return jsonify({'success': False, 'error': '用户不存在'})

    # 返回公开信息
    public_info = {
        'id': user['id'],
        'username': user['username'],
        'full_name': user['full_name'],
        'avatar_url': user['avatar_url'],
        'bio': user['bio'],
        'created_at': user['created_at'],
        'is_premium': user['is_premium'],
        'total_strategies': user['total_strategies'],
        'total_backtests': user['total_backtests'],
        'win_rate': user['win_rate'],
        'followers': user['followers'],
        'following': user['following']
    }

    return jsonify({'success': True, 'user': public_info})

@app.route('/auth/follow', methods=['POST'])
def follow_user():
    """关注用户"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        target_username = data.get('username')

        if not target_username:
            return jsonify({'success': False, 'error': '请指定要关注的用户'})

        current_username = session['username']
        if current_username == target_username:
            return jsonify({'success': False, 'error': '不能关注自己'})

        current_user = MOCK_USERS.get(current_username)
        target_user = MOCK_USERS.get(target_username)

        if not current_user or not target_user:
            return jsonify({'success': False, 'error': '用户不存在'})

        # 模拟关注操作
        current_user['following'] += 1
        target_user['followers'] += 1

        return jsonify({
            'success': True,
            'message': f'已关注 {target_user["full_name"]}',
            'following': current_user['following'],
            'target_followers': target_user['followers']
        })

    except Exception as e:
        logger.error(f"关注用户失败: {e}")
        return jsonify({'success': False, 'error': '关注失败，请稍后重试'})

# ==================== 2FA 双因素认证相关路由 ====================

def generate_backup_codes():
    """生成备用验证码"""
    import secrets
    import string
    codes = []
    for _ in range(10):
        code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
        codes.append(f"{code[:4]}-{code[4:]}")
    return codes

@app.route('/auth/2fa/setup', methods=['POST'])
def setup_2fa():
    """设置2FA"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        username = session['username']
        user = MOCK_USERS.get(username)

        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        if user['two_factor_enabled']:
            return jsonify({'success': False, 'error': '2FA已经启用'})

        # 生成密钥
        secret = pyotp.random_base32()

        # 生成QR码
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user['email'],
            issuer_name='QuantTradeX'
        )

        # 创建QR码图片
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # 转换为base64
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()

        # 生成备用验证码
        backup_codes = generate_backup_codes()

        # 临时保存密钥（实际应用中应该加密存储）
        session['temp_2fa_secret'] = secret
        session['temp_backup_codes'] = backup_codes

        return jsonify({
            'success': True,
            'secret': secret,
            'qr_code': f'data:image/png;base64,{img_base64}',
            'backup_codes': backup_codes,
            'manual_entry_key': secret
        })

    except Exception as e:
        logger.error(f"设置2FA失败: {e}")
        return jsonify({'success': False, 'error': '设置2FA失败，请稍后重试'})

@app.route('/auth/2fa/verify', methods=['POST'])
def verify_2fa_setup():
    """验证并启用2FA"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        code = data.get('code')

        if not code:
            return jsonify({'success': False, 'error': '请输入验证码'})

        username = session['username']
        user = MOCK_USERS.get(username)
        secret = session.get('temp_2fa_secret')
        backup_codes = session.get('temp_backup_codes')

        if not user or not secret:
            return jsonify({'success': False, 'error': '设置信息不完整，请重新设置'})

        # 验证TOTP码
        totp = pyotp.TOTP(secret)
        if not totp.verify(code):
            return jsonify({'success': False, 'error': '验证码错误，请重试'})

        # 启用2FA
        user['two_factor_enabled'] = True
        user['two_factor_secret'] = secret
        user['backup_codes'] = backup_codes

        # 清理临时数据
        session.pop('temp_2fa_secret', None)
        session.pop('temp_backup_codes', None)

        return jsonify({
            'success': True,
            'message': '2FA已成功启用',
            'backup_codes': backup_codes
        })

    except Exception as e:
        logger.error(f"验证2FA失败: {e}")
        return jsonify({'success': False, 'error': '验证失败，请稍后重试'})

@app.route('/auth/2fa/disable', methods=['POST'])
def disable_2fa():
    """禁用2FA"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        password = data.get('password')
        code = data.get('code')

        if not password:
            return jsonify({'success': False, 'error': '请输入密码'})

        username = session['username']
        user = MOCK_USERS.get(username)

        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        if not user['two_factor_enabled']:
            return jsonify({'success': False, 'error': '2FA未启用'})

        # 验证密码
        if user['password'] != password:
            return jsonify({'success': False, 'error': '密码错误'})

        # 验证2FA码或备用码
        if code:
            totp = pyotp.TOTP(user['two_factor_secret'])
            if not totp.verify(code) and code not in user['backup_codes']:
                return jsonify({'success': False, 'error': '验证码错误'})
        else:
            return jsonify({'success': False, 'error': '请输入2FA验证码'})

        # 禁用2FA
        user['two_factor_enabled'] = False
        user['two_factor_secret'] = None
        user['backup_codes'] = []

        return jsonify({
            'success': True,
            'message': '2FA已禁用'
        })

    except Exception as e:
        logger.error(f"禁用2FA失败: {e}")
        return jsonify({'success': False, 'error': '禁用失败，请稍后重试'})

@app.route('/auth/2fa/status')
def get_2fa_status():
    """获取2FA状态"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    username = session['username']
    user = MOCK_USERS.get(username)

    if not user:
        return jsonify({'success': False, 'error': '用户不存在'})

    return jsonify({
        'success': True,
        'enabled': user['two_factor_enabled'],
        'backup_codes_count': len(user.get('backup_codes', []))
    })

@app.route('/auth/2fa/backup-codes', methods=['POST'])
def regenerate_backup_codes():
    """重新生成备用验证码"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        password = data.get('password')

        if not password:
            return jsonify({'success': False, 'error': '请输入密码'})

        username = session['username']
        user = MOCK_USERS.get(username)

        if not user:
            return jsonify({'success': False, 'error': '用户不存在'})

        if not user['two_factor_enabled']:
            return jsonify({'success': False, 'error': '2FA未启用'})

        # 验证密码
        if user['password'] != password:
            return jsonify({'success': False, 'error': '密码错误'})

        # 生成新的备用验证码
        backup_codes = generate_backup_codes()
        user['backup_codes'] = backup_codes

        return jsonify({
            'success': True,
            'message': '备用验证码已重新生成',
            'backup_codes': backup_codes
        })

    except Exception as e:
        logger.error(f"重新生成备用验证码失败: {e}")
        return jsonify({'success': False, 'error': '生成失败，请稍后重试'})

# 修改登录路由以支持2FA
@app.route('/auth/login/2fa', methods=['POST'])
def login_with_2fa():
    """2FA登录验证"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        code = data.get('code')

        if not username or not password:
            return jsonify({'success': False, 'error': '用户名和密码不能为空'})

        # 检查用户是否存在
        user = MOCK_USERS.get(username)
        if not user:
            return jsonify({'success': False, 'error': '用户名或密码错误'})

        # 验证密码
        if user['password'] != password:
            return jsonify({'success': False, 'error': '用户名或密码错误'})

        # 检查是否启用2FA
        if user['two_factor_enabled']:
            if not code:
                return jsonify({
                    'success': False,
                    'require_2fa': True,
                    'message': '请输入2FA验证码'
                })

            # 验证2FA码
            totp = pyotp.TOTP(user['two_factor_secret'])
            is_backup_code = code in user['backup_codes']

            if not totp.verify(code) and not is_backup_code:
                return jsonify({'success': False, 'error': '2FA验证码错误'})

            # 如果使用了备用码，则移除它
            if is_backup_code:
                user['backup_codes'].remove(code)

        # 更新最后登录时间
        user['last_login'] = datetime.now().isoformat()

        # 登录成功，保存到session
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['role'] = user['role']
        session['is_premium'] = user['is_premium']
        session['premium_expires'] = user.get('premium_expires')

        # 返回用户信息（不包含密码和2FA密钥）
        user_info = {k: v for k, v in user.items() if k not in ['password', 'two_factor_secret']}

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': user_info
        })

    except Exception as e:
        logger.error(f"2FA登录失败: {e}")
        return jsonify({'success': False, 'error': '登录失败，请稍后重试'})

# API路由
@app.route('/api/stock/<symbol>')
def get_stock(symbol):
    """获取股票数据API"""
    period = request.args.get('period', '1mo')
    data = quanttradex_service.get_stock_data(symbol.upper(), period)

    if data:
        indicators = quanttradex_service.calculate_indicators(data)
        data['indicators'] = indicators
        return jsonify({'success': True, 'data': data})
    else:
        return jsonify({'success': False, 'error': '无法获取股票数据'})

@app.route('/api/market/overview')
def get_market_overview():
    """获取市场概览API"""
    try:
        # 主要指数和热门股票
        symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN', 'META']
        market_data = []

        for symbol in symbols:
            try:
                # 获取股票数据
                data = quanttradex_service.get_stock_data(symbol, '2d')
                if data and len(data.get('close', [])) >= 2:
                    current_price = data['close'][-1]
                    previous_price = data['close'][-2]
                    change = current_price - previous_price
                    change_pct = (change / previous_price) * 100

                    market_data.append({
                        'symbol': symbol,
                        'price': round(current_price, 2),
                        'change': round(change, 2),
                        'change_percent': round(change_pct, 2)
                    })
                else:
                    # 使用模拟数据
                    import random
                    base_prices = {
                        'SPY': 450.0, 'QQQ': 380.0, 'AAPL': 180.0, 'MSFT': 380.0,
                        'GOOGL': 140.0, 'TSLA': 250.0, 'AMZN': 150.0, 'META': 320.0
                    }
                    base_price = base_prices.get(symbol, 100.0)
                    change_pct = random.uniform(-3.0, 3.0)
                    change = base_price * change_pct / 100
                    current_price = base_price + change

                    market_data.append({
                        'symbol': symbol,
                        'price': round(current_price, 2),
                        'change': round(change, 2),
                        'change_percent': round(change_pct, 2)
                    })
            except Exception as e:
                logger.warning(f"获取 {symbol} 数据失败: {e}")
                continue

        return jsonify({
            'success': True,
            'watchlist': market_data,  # 保持与前端期望的字段名一致
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        return jsonify({'success': False, 'error': '获取市场概览失败'})

# 用户关注列表存储
USER_WATCHLISTS = {}

@app.route('/api/watchlist', methods=['GET', 'POST', 'DELETE'])
def handle_watchlist():
    """处理用户关注列表"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    username = session['username']

    if request.method == 'GET':
        # 使用数据库用户管理器获取关注列表
        if user_manager:
            user_watchlist = user_manager.get_user_watchlist(username)
        else:
            # 回退到内存存储
            user_watchlist = USER_WATCHLISTS.get(username, [])

        results = []

        for item in user_watchlist:
            symbol = item['symbol']
            data_type = item.get('type', 'stock')

            try:
                if data_type == 'stock':
                    data = quanttradex_service.get_stock_data(symbol, '1d')
                    if data:
                        indicators = quanttradex_service.calculate_indicators(data)
                        results.append({
                            'symbol': symbol,
                            'type': data_type,
                            'name': item.get('name', symbol),
                            'price': indicators.get('current_price', 0),
                            'change': indicators.get('price_change', 0),
                            'change_pct': indicators.get('price_change_pct', 0),
                            'volume': indicators.get('volume', 0),
                            'market_cap': indicators.get('market_cap', 0),
                            'added_at': item.get('added_at', datetime.now().isoformat())
                        })
                else:
                    # 处理其他类型的数据（加密货币、外汇等）
                    results.append({
                        'symbol': symbol,
                        'type': data_type,
                        'name': item.get('name', symbol),
                        'price': 0,
                        'change': 0,
                        'change_pct': 0,
                        'volume': 0,
                        'market_cap': 0,
                        'added_at': item.get('added_at', datetime.now().isoformat()),
                        'note': '暂不支持此类型数据'
                    })
            except Exception as e:
                logger.error(f"获取 {symbol} 数据失败: {e}")
                results.append({
                    'symbol': symbol,
                    'type': data_type,
                    'name': item.get('name', symbol),
                    'price': 0,
                    'change': 0,
                    'change_pct': 0,
                    'volume': 0,
                    'market_cap': 0,
                    'added_at': item.get('added_at', datetime.now().isoformat()),
                    'error': '数据获取失败'
                })

        return jsonify({
            'success': True,
            'data': results,
            'total': len(results),
            'timestamp': datetime.now().isoformat()
        })

    elif request.method == 'POST':
        # 添加到关注列表
        try:
            data = request.get_json()
            symbol = data.get('symbol', '').upper().strip()
            data_type = data.get('type', 'stock')
            name = data.get('name', symbol)

            if not symbol:
                return jsonify({'success': False, 'error': '交易品种代码不能为空'})

            # 使用数据库用户管理器添加关注
            if user_manager:
                success, message = user_manager.add_to_watchlist(username, symbol, data_type, name)
                if success:
                    watchlist_item = {
                        'symbol': symbol,
                        'type': data_type,
                        'name': name,
                        'added_at': datetime.now().isoformat()
                    }
                    return jsonify({
                        'success': True,
                        'message': f'已添加 {symbol} 到关注列表',
                        'item': watchlist_item
                    })
                else:
                    return jsonify({'success': False, 'error': message})
            else:
                # 回退到内存存储
                # 初始化用户关注列表
                if username not in USER_WATCHLISTS:
                    USER_WATCHLISTS[username] = []

                user_watchlist = USER_WATCHLISTS[username]

                # 检查是否已存在
                if any(item['symbol'] == symbol and item.get('type', 'stock') == data_type for item in user_watchlist):
                    return jsonify({'success': False, 'error': '该品种已在关注列表中'})

                # 添加到关注列表
                watchlist_item = {
                    'symbol': symbol,
                    'type': data_type,
                    'name': name,
                    'added_at': datetime.now().isoformat()
                }

                user_watchlist.append(watchlist_item)

                return jsonify({
                    'success': True,
                    'message': f'已添加 {symbol} 到关注列表',
                    'item': watchlist_item
                })

        except Exception as e:
            logger.error(f"添加关注失败: {e}")
            return jsonify({'success': False, 'error': '添加失败，请稍后重试'})

    elif request.method == 'DELETE':
        # 从关注列表移除
        try:
            data = request.get_json()
            symbol = data.get('symbol', '').upper().strip()
            data_type = data.get('type', 'stock')

            if not symbol:
                return jsonify({'success': False, 'error': '交易品种代码不能为空'})

            # 使用数据库用户管理器移除关注
            if user_manager:
                success, message = user_manager.remove_from_watchlist(username, symbol, data_type)
                if success:
                    return jsonify({
                        'success': True,
                        'message': f'已从关注列表移除 {symbol}'
                    })
                else:
                    return jsonify({'success': False, 'error': message})
            else:
                # 回退到内存存储
                if username not in USER_WATCHLISTS:
                    return jsonify({'success': False, 'error': '关注列表为空'})

                user_watchlist = USER_WATCHLISTS[username]

                # 查找并移除
                original_length = len(user_watchlist)
                USER_WATCHLISTS[username] = [
                    item for item in user_watchlist
                    if not (item['symbol'] == symbol and item.get('type', 'stock') == data_type)
                ]

                if len(USER_WATCHLISTS[username]) == original_length:
                    return jsonify({'success': False, 'error': '该品种不在关注列表中'})

                return jsonify({
                    'success': True,
                    'message': f'已从关注列表移除 {symbol}'
                })

        except Exception as e:
            logger.error(f"移除关注失败: {e}")
            return jsonify({'success': False, 'error': '移除失败，请稍后重试'})

@app.route('/api/data-providers')
def get_data_providers():
    """获取数据提供商状态"""
    try:
        if api_provider_manager:
            providers = api_provider_manager.get_provider_status()
            return jsonify({
                'success': True,
                'providers': providers,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'API配置模块未加载',
                'providers': {}
            })
    except Exception as e:
        logger.error(f"获取数据提供商状态失败: {e}")
        return jsonify({'success': False, 'error': '获取提供商状态失败'})

@app.route('/api/data-providers/test/<provider_id>')
def test_data_provider(provider_id):
    """测试数据提供商连接"""
    try:
        if not unified_api:
            return jsonify({'success': False, 'error': 'API配置模块未加载'})

        # 测试不同类型的数据获取
        test_results = {}

        if provider_id == 'alpha_vantage':
            # 测试股票数据
            data = unified_api.get_stock_data('AAPL', '1d', provider_id)
            test_results['stock_data'] = data is not None
        elif provider_id == 'coingecko':
            # 测试加密货币数据
            data = unified_api.get_crypto_data('bitcoin', '1d', provider_id)
            test_results['crypto_data'] = data is not None
        elif provider_id == 'yahoo_finance':
            # 测试Yahoo Finance
            data = unified_api.get_stock_data('AAPL', '1d', provider_id)
            test_results['stock_data'] = data is not None
        elif provider_id == 'twelve_data':
            # 测试Twelve Data
            data = unified_api.get_stock_data('AAPL', '1d', provider_id)
            test_results['stock_data'] = data is not None
        else:
            return jsonify({'success': False, 'error': '不支持的提供商'})

        return jsonify({
            'success': True,
            'provider_id': provider_id,
            'test_results': test_results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"测试数据提供商失败: {e}")
        return jsonify({'success': False, 'error': f'测试失败: {str(e)}'})

@app.route('/api/data-providers/config', methods=['POST'])
def update_provider_config():
    """更新数据提供商配置"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    # 只有管理员可以更新配置
    user = MOCK_USERS.get(session['username'])
    if not user or user.get('role') != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        data = request.get_json()
        provider_id = data.get('provider_id')
        api_key = data.get('api_key')

        if not provider_id or not api_key:
            return jsonify({'success': False, 'error': '提供商ID和API密钥不能为空'})

        # 这里应该更新配置文件或环境变量
        # 为了演示，我们只返回成功消息
        return jsonify({
            'success': True,
            'message': f'{provider_id} API密钥已更新',
            'note': '请重启应用以使配置生效'
        })

    except Exception as e:
        logger.error(f"更新提供商配置失败: {e}")
        return jsonify({'success': False, 'error': '更新配置失败'})

# ==================== 支付相关路由 ====================

@app.route('/api/payment/methods')
def get_payment_methods():
    """获取支持的支付方式"""
    try:
        if not payment_service:
            return jsonify({'success': False, 'error': '支付服务未启用'})

        methods = payment_service.get_supported_methods()
        return jsonify({
            'success': True,
            'methods': methods,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取支付方式失败: {e}")
        return jsonify({'success': False, 'error': '获取支付方式失败'})

@app.route('/api/payment/order/<order_id>')
def get_payment_order(order_id):
    """获取支付订单信息"""
    try:
        if not payment_service:
            return jsonify({'success': False, 'error': '支付服务未启用'})

        order = payment_service.get_order(order_id)
        if not order:
            return jsonify({'success': False, 'error': '订单不存在'})

        return jsonify({
            'success': True,
            'order': {
                'order_id': order.order_id,
                'amount': order.amount,
                'currency': order.currency,
                'description': order.description,
                'status': order.status,
                'payment_method': order.payment_method,
                'created_at': order.created_at.isoformat(),
                'paid_at': order.paid_at.isoformat() if order.paid_at else None,
                'transaction_id': order.transaction_id
            }
        })
    except Exception as e:
        logger.error(f"获取支付订单失败: {e}")
        return jsonify({'success': False, 'error': '获取订单信息失败'})

@app.route('/payment/mock/<order_id>')
def mock_payment_page(order_id):
    """模拟支付页面"""
    if not payment_service:
        return "支付服务未启用", 500

    order = payment_service.get_order(order_id)
    if not order:
        return "订单不存在", 404

    if order.status != 'pending':
        return "订单状态无效", 400

    # 返回模拟支付页面
    return f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模拟支付 - QuantTradeX</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
            .payment-card {{ background: rgba(255,255,255,0.95); border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }}
        </style>
    </head>
    <body>
        <div class="container d-flex align-items-center justify-content-center min-vh-100">
            <div class="payment-card p-5" style="max-width: 500px; width: 100%;">
                <div class="text-center mb-4">
                    <h3 class="text-primary">模拟支付</h3>
                    <p class="text-muted">这是一个模拟支付页面，用于测试</p>
                </div>

                <div class="mb-4">
                    <div class="row">
                        <div class="col-6"><strong>订单号:</strong></div>
                        <div class="col-6">{order.order_id}</div>
                    </div>
                    <div class="row">
                        <div class="col-6"><strong>金额:</strong></div>
                        <div class="col-6 text-danger">¥{order.amount}</div>
                    </div>
                    <div class="row">
                        <div class="col-6"><strong>商品:</strong></div>
                        <div class="col-6">{order.description}</div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-lg" onclick="processPayment()">
                        <i class="fas fa-credit-card me-2"></i>确认支付
                    </button>
                    <button class="btn btn-outline-secondary" onclick="cancelPayment()">
                        取消支付
                    </button>
                </div>

                <div id="result" class="mt-3"></div>
            </div>
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
        <script>
            async function processPayment() {{
                try {{
                    const response = await fetch('/api/payment/mock/{order_id}', {{
                        method: 'POST'
                    }});
                    const result = await response.json();

                    if (result.success) {{
                        document.getElementById('result').innerHTML = `
                            <div class="alert alert-success">
                                <h5>支付成功！</h5>
                                <p>交易号: ${{result.transaction_id}}</p>
                                <p>正在跳转...</p>
                            </div>
                        `;

                        setTimeout(() => {{
                            window.location.href = '/auth/upgrade/complete/{order_id}';
                        }}, 2000);
                    }} else {{
                        document.getElementById('result').innerHTML = `
                            <div class="alert alert-danger">支付失败: ${{result.error}}</div>
                        `;
                    }}
                }} catch (error) {{
                    document.getElementById('result').innerHTML = `
                        <div class="alert alert-danger">网络错误，请稍后重试</div>
                    `;
                }}
            }}

            function cancelPayment() {{
                if (confirm('确定要取消支付吗？')) {{
                    window.location.href = '/';
                }}
            }}
        </script>
    </body>
    </html>
    """

@app.route('/api/payment/mock/<order_id>', methods=['POST'])
def process_mock_payment(order_id):
    """处理模拟支付"""
    try:
        if not payment_service:
            return jsonify({'success': False, 'error': '支付服务未启用'})

        result = payment_service.process_mock_payment(order_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"处理模拟支付失败: {e}")
        return jsonify({'success': False, 'error': '支付处理失败'})

# 策略相关API - 模拟数据
@app.route('/api/strategies', methods=['GET', 'POST'])
def strategies_api():
    """策略API"""
    if request.method == 'POST':
        data = request.get_json()
        # 模拟创建策略
        strategy = {
            'id': 1,
            'name': data['name'],
            'description': data.get('description', ''),
            'category': data.get('category', ''),
            'tags': data.get('tags', []),
            'is_public': data.get('is_public', False),
            'is_premium': data.get('is_premium', False),
            'price': data.get('price', 0),
            'rating': 4.5,
            'downloads': 0,
            'author': 'demo_user',
            'created_at': datetime.now().isoformat()
        }
        return jsonify({'success': True, 'strategy': strategy})

    # GET请求 - 从生成的数据返回策略列表
    try:
        # 加载策略代码映射
        with open('strategy_codes_mapping.json', 'r', encoding='utf-8') as f:
            strategy_codes = json.load(f)

        strategies = []
        for strategy_id, strategy_data in strategy_codes.items():
            # 根据策略类型分配分类
            strategy_type = strategy_data.get('type', 'trend_following')
            if strategy_type == 'trend_following':
                category = 'trend'
            elif strategy_type == 'mean_reversion':
                category = 'mean_reversion'
            elif strategy_type == 'machine_learning':
                category = 'momentum'
            else:
                category = 'trend'

            # 生成随机的评分和下载量
            import random
            rating = round(random.uniform(3.5, 5.0), 1)
            downloads = random.randint(50, 2000)

            strategy = {
                'id': int(strategy_id),
                'name': strategy_data['name'],
                'description': f"基于{strategy_data.get('indicator', '技术指标')}的{strategy_data.get('timeframe', '日线')}策略，适用于{strategy_data.get('asset_class', '股票')}市场。",
                'category': category,
                'tags': [strategy_data.get('indicator', '技术指标'), strategy_data.get('timeframe', '日线'), strategy_data.get('asset_class', '股票')],
                'is_public': True,
                'is_premium': random.choice([True, False]),
                'price': random.choice([0, 29.99, 49.99, 99.99]) if random.choice([True, False]) else 0,
                'rating': rating,
                'downloads': downloads,
                'author': random.choice(['AlgoTrader', 'QuantMaster', 'TechAnalyst', 'MLQuant', 'RiskManager']),
                'created_at': '2025-01-15T10:30:00'
            }
            strategies.append(strategy)
    except:
        # 回退到硬编码数据
        strategies = [
            {
                'id': 1,
                'name': '双均线策略',
                'description': '基于20日和50日移动平均线的趋势跟踪策略，适合中长期投资',
                'category': 'trend',
                'tags': ['移动平均', '趋势跟踪', '中长期'],
                'is_public': True,
                'is_premium': False,
                'price': 0,
                'rating': 4.5,
                'downloads': 1234,
                'author': 'AlgoTrader',
                'created_at': '2025-01-15T10:30:00'
            },
            {
                'id': 2,
                'name': 'RSI均值回归策略',
                'description': '基于RSI指标的均值回归策略，在超买超卖区域寻找反转机会',
                'category': 'mean_reversion',
                'tags': ['RSI', '均值回归', '技术指标'],
                'is_public': True,
                'is_premium': True,
                'price': 99.99,
                'rating': 4.2,
                'downloads': 856,
                'author': 'QuantMaster',
                'created_at': '2025-01-10T14:20:00'
            }
        ]

    # 处理筛选和排序参数
    category = request.args.get('category', '')
    sort_by = request.args.get('sort_by', 'rating')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 12))

    # 筛选策略
    filtered_strategies = strategies
    if category:
        filtered_strategies = [s for s in strategies if s['category'] == category]

    # 排序策略
    if sort_by == 'rating':
        filtered_strategies.sort(key=lambda x: x['rating'], reverse=True)
    elif sort_by == 'downloads':
        filtered_strategies.sort(key=lambda x: x['downloads'], reverse=True)
    elif sort_by == 'created_at':
        filtered_strategies.sort(key=lambda x: x['created_at'], reverse=True)
    elif sort_by == 'price':
        filtered_strategies.sort(key=lambda x: x['price'])

    # 分页处理
    total = len(filtered_strategies)
    pages = (total + per_page - 1) // per_page
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_strategies = filtered_strategies[start_idx:end_idx]

    return jsonify({
        'success': True,
        'strategies': paginated_strategies,
        'total': total,
        'pages': pages,
        'current_page': page
    })

@app.route('/api/strategies/<int:strategy_id>')
def get_strategy(strategy_id):
    """获取单个策略"""
    # 从生成的数据加载策略信息
    strategy_info = load_strategy_info(strategy_id)

    strategy = {
        'id': strategy_id,
        'name': strategy_info.get('name', f'策略{strategy_id}'),
        'description': f"基于{strategy_info.get('name', '量化交易')}的专业策略，经过严格回测验证。",
        'code': strategy_info.get('code', '# 策略代码未找到\npass'),
        'category': 'trend',
        'tags': ['量化交易', '策略优化'],
        'is_public': True,
        'is_premium': False,
        'price': 0,
        'rating': 4.5,
        'downloads': 234,
        'author': 'AlgoTrader',
        'created_at': '2025-01-15T10:30:00'
    }

    return jsonify({'success': True, 'strategy': strategy})

@app.route('/api/strategy/save', methods=['POST'])
def save_strategy():
    """保存策略"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['name', 'code']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'{field} 不能为空'})

        # 模拟保存策略
        strategy = {
            'id': 100,  # 新策略ID
            'name': data['name'],
            'description': data.get('description', ''),
            'type': data.get('type', 'trend'),
            'asset_class': data.get('asset_class', 'stocks'),
            'code': data['code'],
            'template': data.get('template'),
            'author': session['username'],
            'is_public': False,
            'is_premium': False,
            'price': 0,
            'rating': 0,
            'downloads': 0,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        # 在实际应用中，这里应该保存到数据库
        # 现在只是模拟返回成功

        return jsonify({
            'success': True,
            'message': '策略保存成功',
            'strategy': strategy
        })

    except Exception as e:
        logger.error(f"保存策略失败: {e}")
        return jsonify({'success': False, 'error': '保存失败，请稍后重试'})

@app.route('/api/strategy/run', methods=['POST'])
def run_strategy():
    """运行策略"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        code = data.get('code', '')

        if not code.strip():
            return jsonify({'success': False, 'error': '策略代码不能为空'})

        # 模拟策略运行
        import time
        import random

        # 模拟运行时间
        time.sleep(1)

        # 生成模拟结果
        results = {
            'total_return': round(random.uniform(-10, 20), 2),
            'sharpe_ratio': round(random.uniform(0.5, 2.5), 2),
            'max_drawdown': round(-random.uniform(2, 17), 2),
            'win_rate': round(random.uniform(40, 80), 1),
            'trades_count': random.randint(20, 120),
            'profit_factor': round(random.uniform(0.8, 2.8), 2),
            'execution_time': round(random.uniform(1, 6), 1),
            'start_date': '2024-01-01',
            'end_date': '2024-12-31',
            'initial_capital': 100000,
            'final_capital': 0
        }

        # 计算最终资金
        results['final_capital'] = results['initial_capital'] * (1 + results['total_return'] / 100)

        return jsonify({
            'success': True,
            'message': '策略运行完成',
            'results': results
        })

    except Exception as e:
        logger.error(f"运行策略失败: {e}")
        return jsonify({'success': False, 'error': '运行失败，请稍后重试'})

# 回测相关API
@app.route('/api/backtest', methods=['POST'])
def run_backtest_api():
    """运行高级回测"""
    try:
        data = request.get_json()

        # 构建策略配置
        strategy_config = {
            'symbol': data['symbol'],
            'start_date': data['start_date'],
            'end_date': data['end_date'],
            'initial_capital': data.get('initial_capital', 100000),
            'strategy_type': data.get('strategy_type', 'buy_and_hold'),
            'parameters': data.get('parameters', {}),
            'interval': data.get('interval', '1d')
        }

        # 运行高级回测
        results = backtest_engine.run_advanced_backtest(strategy_config)

        if 'error' in results:
            return jsonify({'success': False, 'error': results['error']})

        # 构建回测记录
        backtest = {
            'id': len(mock_backtests) + 1,
            'name': data.get('name', f"{data['symbol']} 高级回测"),
            'symbol': data['symbol'],
            'strategy_type': strategy_config['strategy_type'],
            'start_date': data['start_date'],
            'end_date': data['end_date'],
            'initial_capital': strategy_config['initial_capital'],
            'parameters': strategy_config['parameters'],
            'results': results,
            'status': 'completed',
            'created_at': datetime.now().isoformat()
        }

        # 添加到模拟数据库
        mock_backtests.append(backtest)

        return jsonify({'success': True, 'backtest': backtest})

    except Exception as e:
        logger.error(f"回测API失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/backtest/advanced', methods=['POST'])
def run_advanced_backtest_api():
    """运行高级回测（详细版本）"""
    try:
        data = request.get_json()

        # 验证必需参数
        required_fields = ['symbol', 'start_date', 'end_date']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必需参数: {field}'})

        # 构建策略配置
        strategy_config = {
            'symbol': data['symbol'].upper(),
            'start_date': data['start_date'],
            'end_date': data['end_date'],
            'initial_capital': data.get('initial_capital', 100000),
            'strategy_type': data.get('strategy_type', 'moving_average_crossover'),
            'parameters': data.get('parameters', {}),
            'interval': data.get('interval', '1d'),
            'benchmark': data.get('benchmark', 'SPY')
        }

        # 运行高级回测
        results = backtest_engine.run_advanced_backtest(strategy_config)

        if 'error' in results:
            return jsonify({'success': False, 'error': results['error']})

        # 生成回测报告
        report = {
            'backtest_id': len(mock_backtests) + 1,
            'name': data.get('name', f"{data['symbol']} 高级策略回测"),
            'symbol': strategy_config['symbol'],
            'strategy_type': strategy_config['strategy_type'],
            'period': {
                'start_date': strategy_config['start_date'],
                'end_date': strategy_config['end_date'],
                'duration_days': (pd.to_datetime(strategy_config['end_date']) - pd.to_datetime(strategy_config['start_date'])).days
            },
            'capital': {
                'initial': strategy_config['initial_capital'],
                'final': results['performance']['final_value'],
                'profit_loss': results['performance']['final_value'] - strategy_config['initial_capital']
            },
            'performance': results['performance'],
            'risk_metrics': results['risk_metrics'],
            'trade_analysis': results['trade_analysis'],
            'equity_curve': results['equity_curve'],
            'drawdown_curve': results['drawdown_curve'],
            'trades': results['trades'],
            'strategy_config': strategy_config,
            'created_at': datetime.now().isoformat(),
            'status': 'completed'
        }

        # 添加到模拟数据库
        mock_backtests.append(report)

        return jsonify({'success': True, 'report': report})

    except Exception as e:
        logger.error(f"高级回测API失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/backtests')
def get_backtests():
    """获取回测列表"""
    # 模拟回测历史
    backtests = [
        {
            'id': 1,
            'name': 'AAPL双均线测试',
            'symbol': 'AAPL',
            'strategy': '双均线策略',
            'author': 'demo_user',
            'results': {
                'total_return': 15.6,
                'max_drawdown': -8.2,
                'sharpe_ratio': 1.34,
                'win_rate': 68.5,
                'trades_count': 24
            },
            'created_at': '2025-01-15T10:30:00'
        },
        {
            'id': 2,
            'name': 'GOOGL RSI策略',
            'symbol': 'GOOGL',
            'strategy': 'RSI反转策略',
            'author': 'demo_user',
            'results': {
                'total_return': -3.2,
                'max_drawdown': -12.5,
                'sharpe_ratio': -0.45,
                'win_rate': 42.1,
                'trades_count': 18
            },
            'created_at': '2025-01-14T16:45:00'
        },
        {
            'id': 3,
            'name': 'TSLA网格交易',
            'symbol': 'TSLA',
            'strategy': '网格交易策略',
            'author': 'trader_pro',
            'results': {
                'total_return': 28.9,
                'max_drawdown': -6.8,
                'sharpe_ratio': 2.15,
                'win_rate': 78.3,
                'trades_count': 156
            },
            'created_at': '2025-01-13T14:20:00'
        },
        {
            'id': 4,
            'name': 'BTC高频套利',
            'symbol': 'BTC-USD',
            'strategy': '高频套利策略',
            'author': 'crypto_king',
            'results': {
                'total_return': 45.7,
                'max_drawdown': -4.2,
                'sharpe_ratio': 3.67,
                'win_rate': 89.2,
                'trades_count': 2341
            },
            'created_at': '2025-01-12T09:15:00'
        },
        {
            'id': 5,
            'name': 'SPY因子选股',
            'symbol': 'SPY',
            'strategy': '多因子选股策略',
            'author': 'quant_master',
            'results': {
                'total_return': 22.4,
                'max_drawdown': -9.1,
                'sharpe_ratio': 1.89,
                'win_rate': 72.6,
                'trades_count': 45
            },
            'created_at': '2025-01-11T16:30:00'
        },
        {
            'id': 6,
            'name': 'NVDA机器学习',
            'symbol': 'NVDA',
            'strategy': '机器学习预测策略',
            'author': 'quant_master',
            'results': {
                'total_return': 67.8,
                'max_drawdown': -15.3,
                'sharpe_ratio': 2.45,
                'win_rate': 76.9,
                'trades_count': 32
            },
            'created_at': '2025-01-10T11:45:00'
        },
        {
            'id': 7,
            'name': 'EUR/USD配对交易',
            'symbol': 'EURUSD',
            'strategy': '配对交易策略',
            'author': 'trader_pro',
            'results': {
                'total_return': 8.3,
                'max_drawdown': -3.7,
                'sharpe_ratio': 1.67,
                'win_rate': 81.2,
                'trades_count': 67
            },
            'created_at': '2025-01-09T13:20:00'
        },
        {
            'id': 8,
            'name': 'VIX情绪策略',
            'symbol': 'VIX',
            'strategy': '情绪指标策略',
            'author': 'algo_trader',
            'results': {
                'total_return': 12.7,
                'max_drawdown': -7.4,
                'sharpe_ratio': 1.23,
                'win_rate': 65.4,
                'trades_count': 28
            },
            'created_at': '2025-01-08T15:10:00'
        }
    ]

    return jsonify({
        'success': True,
        'backtests': backtests,
        'total': len(backtests),
        'pages': 1,
        'current_page': 1
    })

# 论坛相关API
@app.route('/api/forum/posts', methods=['GET', 'POST'])
def forum_posts_api():
    """论坛帖子API"""
    if request.method == 'POST':
        if 'username' not in session:
            return jsonify({'success': False, 'error': '请先登录'})

        data = request.get_json()

        # 验证必填字段
        if not data.get('title') or not data.get('content'):
            return jsonify({'success': False, 'error': '标题和内容不能为空'})

        # 模拟创建帖子
        post = {
            'id': random.randint(1000, 9999),
            'title': data['title'],
            'content': data['content'],
            'category': data.get('category', 'general'),
            'tags': data.get('tags', '').split(',') if data.get('tags') else [],
            'views': 0,
            'likes': 0,
            'is_pinned': False,
            'is_locked': False,
            'author': session['username'],
            'reply_count': 0,
            'created_at': datetime.now().isoformat()
        }
        return jsonify({'success': True, 'post': post, 'message': '帖子发布成功'})

    # GET请求 - 从生成的数据文件读取帖子
    try:
        # 使用新的数据加载函数
        posts = load_generated_forum_posts()

        # 处理分页和筛选
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        category = request.args.get('category', '')
        search = request.args.get('search', '')

        # 筛选帖子
        filtered_posts = posts
        if category:
            filtered_posts = [p for p in filtered_posts if p['category'] == category]
        if search:
            filtered_posts = [p for p in filtered_posts if search.lower() in p['title'].lower() or search.lower() in p['content'].lower()]

        # 排序：置顶帖子在前，然后按创建时间倒序
        filtered_posts.sort(key=lambda x: (not x['is_pinned'], x['created_at']), reverse=True)

        # 分页
        total = len(filtered_posts)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_posts = filtered_posts[start:end]

        return jsonify({
            'success': True,
            'posts': paginated_posts,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'current_page': page,
            'per_page': per_page
        })

    except Exception as e:
        logger.error(f"获取论坛帖子失败: {e}")
        return jsonify({'success': False, 'error': '获取帖子失败'})

@app.route('/api/forum/posts/<int:post_id>')
def get_forum_post(post_id):
    """获取单个论坛帖子详情"""
    try:
        posts = load_generated_forum_posts()

        # 查找指定ID的帖子
        post = None
        for p in posts:
            if p['id'] == post_id:
                post = p
                break

        if not post:
            return jsonify({'success': False, 'error': '帖子不存在'})

        # 增加浏览量（模拟）
        post['views'] = post.get('views', 0) + 1

        return jsonify({'success': True, 'post': post})

    except Exception as e:
        logger.error(f"获取论坛帖子详情失败: {e}")
        return jsonify({'success': False, 'error': '获取帖子详情失败'})

@app.route('/api/forum/stats')
def forum_stats_api():
    """论坛统计信息API"""
    try:
        # 尝试读取生成的统计数据
        import os
        if os.path.exists('forum_api_data.json'):
            with open('forum_api_data.json', 'r', encoding='utf-8') as f:
                forum_data = json.load(f)
                stats = forum_data.get('stats', {})
        else:
            # 默认统计数据
            stats = {
                'total_posts': 50,
                'total_users': 25,
                'total_views': 50000,
                'total_likes': 5000,
                'pinned_posts': 3,
                'featured_posts': 5
            }

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"获取论坛统计失败: {e}")
        return jsonify({'success': False, 'error': '获取统计数据失败'})

@app.route('/api/forum/categories')
def forum_categories_api():
    """论坛分类API"""
    categories = [
        {'id': 'general', 'name': '综合讨论', 'icon': 'fas fa-comments', 'description': '一般性讨论和交流'},
        {'id': 'strategy', 'name': '策略分享', 'icon': 'fas fa-code', 'description': '量化策略分享和讨论'},
        {'id': 'market', 'name': '市场分析', 'icon': 'fas fa-chart-line', 'description': '市场走势和分析'},
        {'id': 'help', 'name': '新手求助', 'icon': 'fas fa-question-circle', 'description': '新手问题和求助'},
        {'id': 'news', 'name': '行业资讯', 'icon': 'fas fa-newspaper', 'description': '行业新闻和资讯'},
        {'id': 'crypto', 'name': '数字货币', 'icon': 'fab fa-bitcoin', 'description': '数字货币相关讨论'},
        {'id': 'risk', 'name': '风险管理', 'icon': 'fas fa-shield-alt', 'description': '风险控制和管理'},
        {'id': 'data', 'name': '数据分析', 'icon': 'fas fa-database', 'description': '数据获取和分析'},
        {'id': 'technology', 'name': '技术讨论', 'icon': 'fas fa-cogs', 'description': '技术实现和工具'},
        {'id': 'education', 'name': '教育资源', 'icon': 'fas fa-graduation-cap', 'description': '学习资源和教程'}
    ]

    return jsonify({
        'success': True,
        'categories': categories
    })

@app.route('/api/forum/hot-topics')
def forum_hot_topics_api():
    """热门话题API"""
    hot_topics = [
        {'tag': '量化策略', 'count': 234, 'trend': 'up'},
        {'tag': '机器学习', 'count': 189, 'trend': 'up'},
        {'tag': '风险管理', 'count': 156, 'trend': 'stable'},
        {'tag': '技术分析', 'count': 123, 'trend': 'down'},
        {'tag': '回测优化', 'count': 98, 'trend': 'up'},
        {'tag': '数字货币', 'count': 87, 'trend': 'up'},
        {'tag': 'Python', 'count': 76, 'trend': 'stable'},
        {'tag': '深度学习', 'count': 65, 'trend': 'up'},
        {'tag': '套利策略', 'count': 54, 'trend': 'stable'},
        {'tag': '期权交易', 'count': 43, 'trend': 'down'}
    ]

    return jsonify({
        'success': True,
        'hot_topics': hot_topics
    })

@app.route('/api/market/crypto/<symbol>')
def get_crypto_data(symbol):
    """获取数字货币数据"""
    try:
        # 模拟数字货币数据
        crypto_data = {
            'BTC': {'name': 'Bitcoin', 'price': 43250.50, 'change_24h': 2.45},
            'ETH': {'name': 'Ethereum', 'price': 2580.75, 'change_24h': -1.23},
            'BNB': {'name': 'Binance Coin', 'price': 315.20, 'change_24h': 0.87},
            'ADA': {'name': 'Cardano', 'price': 0.485, 'change_24h': 3.21},
            'SOL': {'name': 'Solana', 'price': 98.45, 'change_24h': -2.15}
        }

        symbol = symbol.upper()
        if symbol not in crypto_data:
            return jsonify({'success': False, 'error': '不支持的数字货币'})

        data = crypto_data[symbol]

        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'name': data['name'],
                'price': data['price'],
                'change_24h': data['change_24h'],
                'volume_24h': random.randint(1000000, 50000000),
                'market_cap': data['price'] * random.randint(18000000, 21000000),
                'circulating_supply': random.randint(18000000, 21000000),
                'timestamp': datetime.now().isoformat(),
                'trading_pairs': ['USDT', 'BUSD', 'ETH', 'BTC']
            }
        })

    except Exception as e:
        logger.error(f"获取数字货币数据失败: {e}")
        return jsonify({'success': False, 'error': '获取数据失败'})

@app.route('/api/market/forex/<pair>')
def get_forex_data(pair):
    """获取外汇数据"""
    try:
        # 模拟外汇数据
        forex_data = {
            'EURUSD': {'rate': 1.0875, 'change': 0.0023},
            'GBPUSD': {'rate': 1.2654, 'change': -0.0045},
            'USDJPY': {'rate': 149.85, 'change': 0.32},
            'USDCNY': {'rate': 7.2345, 'change': 0.0156},
            'AUDUSD': {'rate': 0.6587, 'change': -0.0012}
        }

        pair = pair.upper()
        if pair not in forex_data:
            return jsonify({'success': False, 'error': '不支持的货币对'})

        data = forex_data[pair]

        return jsonify({
            'success': True,
            'data': {
                'pair': pair,
                'rate': data['rate'],
                'change': data['change'],
                'change_percent': round((data['change'] / data['rate']) * 100, 4),
                'bid': data['rate'] - 0.0001,
                'ask': data['rate'] + 0.0001,
                'spread': 0.0002,
                'timestamp': datetime.now().isoformat(),
                'session': 'London' if datetime.now().hour < 16 else 'New York'
            }
        })

    except Exception as e:
        logger.error(f"获取外汇数据失败: {e}")
        return jsonify({'success': False, 'error': '获取数据失败'})

@app.route('/api/market/futures/<symbol>')
def get_futures_data(symbol):
    """获取期货数据"""
    try:
        # 模拟期货数据
        futures_data = {
            'CL': {'name': '原油期货', 'price': 78.45, 'change': 1.23, 'contract': 'CL2024M'},
            'GC': {'name': '黄金期货', 'price': 2045.60, 'change': -5.40, 'contract': 'GC2024M'},
            'SI': {'name': '白银期货', 'price': 24.85, 'change': -0.15, 'contract': 'SI2024M'},
            'ES': {'name': 'E-mini S&P 500', 'price': 4785.25, 'change': 12.50, 'contract': 'ES2024M'},
            'NQ': {'name': 'E-mini Nasdaq', 'price': 16850.75, 'change': 45.25, 'contract': 'NQ2024M'}
        }

        symbol = symbol.upper()
        if symbol not in futures_data:
            return jsonify({'success': False, 'error': '不支持的期货合约'})

        data = futures_data[symbol]

        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'name': data['name'],
                'price': data['price'],
                'change': data['change'],
                'change_percent': round((data['change'] / data['price']) * 100, 2),
                'contract': data['contract'],
                'volume': random.randint(10000, 500000),
                'open_interest': random.randint(50000, 1000000),
                'margin_requirement': data['price'] * 0.05,  # 5% 保证金
                'contract_size': 1000 if symbol in ['CL', 'GC', 'SI'] else 50,
                'expiry_date': '2024-06-21',
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"获取期货数据失败: {e}")
        return jsonify({'success': False, 'error': '获取数据失败'})

@app.route('/api/system/status')
def system_status():
    """系统状态检查"""
    status = {
        'redis': False,
        'database': False,
        'timestamp': datetime.now().isoformat()
    }

    try:
        if quanttradex_service.redis_client:
            quanttradex_service.redis_client.ping()
            status['redis'] = True
    except:
        pass

    try:
        if quanttradex_service.db_connection:
            with quanttradex_service.db_connection.cursor() as cursor:
                cursor.execute('SELECT 1')
            status['database'] = True
    except:
        pass

    return jsonify(status)

@app.route('/api/market/indicators/<symbol>')
def get_technical_indicators(symbol):
    """获取技术指标数据"""
    try:
        # 模拟技术指标计算
        import random
        import math

        # 生成模拟价格数据
        base_price = 150.0
        prices = []
        for i in range(50):
            price = base_price + random.uniform(-10, 10) + math.sin(i * 0.1) * 5
            prices.append(round(price, 2))

        # 计算技术指标
        def calculate_sma(data, period):
            if len(data) < period:
                return None
            return round(sum(data[-period:]) / period, 2)

        def calculate_rsi(data, period=14):
            if len(data) < period + 1:
                return None

            gains = []
            losses = []

            for i in range(1, len(data)):
                change = data[i] - data[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period

            if avg_loss == 0:
                return 100

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return round(rsi, 2)

        indicators = {
            'sma_20': calculate_sma(prices, 20),
            'sma_50': calculate_sma(prices, 50),
            'rsi_14': calculate_rsi(prices, 14),
            'current_price': prices[-1],
            'support_level': min(prices[-20:]),
            'resistance_level': max(prices[-20:]),
            'volatility': round(sum(abs(prices[i] - prices[i-1]) for i in range(1, len(prices))) / len(prices), 2),
            'trend': 'bullish' if prices[-1] > calculate_sma(prices, 20) else 'bearish'
        }

        return jsonify({
            'success': True,
            'symbol': symbol,
            'indicators': indicators,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"计算技术指标失败: {e}")
        return jsonify({'success': False, 'error': '计算失败'})

# 实盘交易API
@app.route('/api/trading/account')
def get_trading_account():
    """获取交易账户信息"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        # 模拟账户信息
        account = {
            'account_id': f"ACC_{session['user_id']:06d}",
            'balance': 50000.00,
            'available_balance': 45000.00,
            'margin_used': 5000.00,
            'unrealized_pnl': 250.50,
            'realized_pnl': 1250.75,
            'equity': 51250.50,
            'margin_level': 1025.01,
            'currency': 'USD',
            'leverage': '1:100',
            'account_type': 'VIP' if session.get('is_premium') else 'Standard'
        }

        return jsonify({
            'success': True,
            'account': account,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取账户信息失败: {e}")
        return jsonify({'success': False, 'error': '获取账户信息失败'})

@app.route('/api/trading/positions')
def get_positions():
    """获取持仓信息"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        # 模拟持仓数据
        positions = [
            {
                'position_id': 'POS_001',
                'symbol': 'AAPL',
                'side': 'long',
                'size': 100,
                'entry_price': 175.50,
                'current_price': 178.25,
                'unrealized_pnl': 275.00,
                'margin_required': 1755.00,
                'open_time': '2025-01-26T10:30:00'
            },
            {
                'position_id': 'POS_002',
                'symbol': 'EURUSD',
                'side': 'short',
                'size': 10000,
                'entry_price': 1.0890,
                'current_price': 1.0875,
                'unrealized_pnl': 15.00,
                'margin_required': 1089.00,
                'open_time': '2025-01-26T14:15:00'
            }
        ]

        return jsonify({
            'success': True,
            'positions': positions,
            'total_positions': len(positions),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取持仓信息失败: {e}")
        return jsonify({'success': False, 'error': '获取持仓信息失败'})

@app.route('/api/trading/orders', methods=['GET', 'POST'])
def handle_orders():
    """处理订单"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    if request.method == 'GET':
        # 获取订单历史
        try:
            orders = [
                {
                    'order_id': 'ORD_001',
                    'symbol': 'AAPL',
                    'side': 'buy',
                    'type': 'market',
                    'size': 100,
                    'price': 175.50,
                    'status': 'filled',
                    'filled_size': 100,
                    'remaining_size': 0,
                    'created_at': '2025-01-26T10:30:00',
                    'filled_at': '2025-01-26T10:30:05'
                },
                {
                    'order_id': 'ORD_002',
                    'symbol': 'GOOGL',
                    'side': 'sell',
                    'type': 'limit',
                    'size': 50,
                    'price': 2650.00,
                    'status': 'pending',
                    'filled_size': 0,
                    'remaining_size': 50,
                    'created_at': '2025-01-26T15:45:00',
                    'filled_at': None
                }
            ]

            return jsonify({
                'success': True,
                'orders': orders,
                'total_orders': len(orders),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"获取订单历史失败: {e}")
            return jsonify({'success': False, 'error': '获取订单历史失败'})

    elif request.method == 'POST':
        # 创建新订单
        try:
            data = request.get_json()

            # 验证订单参数
            required_fields = ['symbol', 'side', 'type', 'size']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'success': False, 'error': f'{field} 不能为空'})

            # 模拟订单创建
            order = {
                'order_id': f"ORD_{random.randint(1000, 9999)}",
                'symbol': data['symbol'].upper(),
                'side': data['side'],
                'type': data['type'],
                'size': data['size'],
                'price': data.get('price'),
                'status': 'pending',
                'filled_size': 0,
                'remaining_size': data['size'],
                'created_at': datetime.now().isoformat(),
                'filled_at': None
            }

            # 如果是市价单，立即成交
            if data['type'] == 'market':
                order['status'] = 'filled'
                order['filled_size'] = data['size']
                order['remaining_size'] = 0
                order['filled_at'] = datetime.now().isoformat()
                order['price'] = 150.00 + random.uniform(-5, 5)  # 模拟成交价格

            return jsonify({
                'success': True,
                'message': '订单创建成功',
                'order': order
            })

        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return jsonify({'success': False, 'error': '创建订单失败'})

# ==================== WebSocket 事件处理 ====================

@socketio.on('connect')
def handle_connect():
    """客户端连接事件"""
    try:
        # 获取用户信息
        user_info = None
        if 'username' in session:
            user_info = {
                'username': session['username'],
                'user_id': session.get('user_id'),
                'role': session.get('role')
            }

        # 添加连接
        realtime_service.add_connection(request.sid, user_info)

        # 发送连接确认
        emit('connected', {
            'status': 'success',
            'message': '实时数据连接已建立',
            'sid': request.sid,
            'timestamp': datetime.now().isoformat()
        })

        logger.info(f"WebSocket连接建立: {request.sid}")

    except Exception as e:
        logger.error(f"WebSocket连接处理失败: {e}")
        emit('error', {'message': '连接失败'})

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接事件"""
    try:
        realtime_service.remove_connection(request.sid)
        logger.info(f"WebSocket连接断开: {request.sid}")
    except Exception as e:
        logger.error(f"WebSocket断开处理失败: {e}")

@socketio.on('subscribe')
def handle_subscribe(data):
    """订阅市场数据"""
    try:
        symbol = data.get('symbol', '').upper()
        data_type = data.get('type', 'stock')

        if not symbol:
            emit('error', {'message': '股票代码不能为空'})
            return

        success = realtime_service.subscribe_symbol(request.sid, symbol, data_type)

        if success:
            emit('subscribed', {
                'symbol': symbol,
                'type': data_type,
                'message': f'已订阅 {symbol} 的实时数据',
                'timestamp': datetime.now().isoformat()
            })
        else:
            emit('error', {'message': '订阅失败'})

    except Exception as e:
        logger.error(f"订阅处理失败: {e}")
        emit('error', {'message': '订阅失败'})

@socketio.on('unsubscribe')
def handle_unsubscribe(data):
    """取消订阅市场数据"""
    try:
        symbol = data.get('symbol', '').upper()
        data_type = data.get('type', 'stock')

        realtime_service.unsubscribe_symbol(request.sid, symbol, data_type)

        emit('unsubscribed', {
            'symbol': symbol,
            'type': data_type,
            'message': f'已取消订阅 {symbol}',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"取消订阅处理失败: {e}")
        emit('error', {'message': '取消订阅失败'})

@socketio.on('get_stats')
def handle_get_stats():
    """获取连接统计"""
    try:
        stats = realtime_service.get_connection_stats()
        emit('stats', {
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取统计失败: {e}")
        emit('error', {'message': '获取统计失败'})

@socketio.on('ping')
def handle_ping():
    """心跳检测"""
    emit('pong', {'timestamp': datetime.now().isoformat()})

# ==================== 实时数据API ====================

@app.route('/api/realtime/stats')
def get_realtime_stats():
    """获取实时数据服务统计"""
    try:
        stats = realtime_service.get_connection_stats()
        return jsonify({
            'success': True,
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取实时统计失败: {e}")
        return jsonify({'success': False, 'error': '获取统计失败'})

@app.route('/api/realtime/connections')
def get_active_connections():
    """获取活跃连接信息"""
    if 'username' not in session or session.get('role') != 'admin':
        return jsonify({'success': False, 'error': '权限不足'})

    try:
        connections = []
        for sid, info in realtime_service.active_connections.items():
            connections.append({
                'sid': sid,
                'connected_at': info['connected_at'].isoformat(),
                'user_info': info['user_info'],
                'subscriptions': list(info['subscriptions'])
            })

        return jsonify({
            'success': True,
            'connections': connections,
            'total': len(connections),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取连接信息失败: {e}")
        return jsonify({'success': False, 'error': '获取连接信息失败'})

if __name__ == '__main__':
    # 启动实时数据服务
    realtime_service.start_service()

    # 创建必要的目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    # 使用SocketIO运行应用
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, allow_unsafe_werkzeug=True)
