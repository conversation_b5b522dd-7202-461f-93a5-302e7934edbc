"""
数据库管理器 - 统一的数据库操作接口
支持PostgreSQL和Redis的高性能数据管理
"""

import psycopg2
import psycopg2.extras
import redis
import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """统一数据库管理器"""
    
    def __init__(self):
        self.pg_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'quanttradex_user',
            'password': 'quanttradex_2024!',
            'database': 'quanttradex'
        }
        
        self.redis_config = {
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'decode_responses': True,
            'socket_connect_timeout': 5,
            'socket_timeout': 5
        }
        
        self.pg_conn = None
        self.redis_conn = None
        self._connect()
    
    def _connect(self):
        """连接数据库"""
        try:
            # 连接PostgreSQL
            self.pg_conn = psycopg2.connect(**self.pg_config)
            self.pg_conn.autocommit = True
            logger.info("PostgreSQL连接成功")

            # 连接Redis（可选）
            try:
                self.redis_conn = redis.Redis(**self.redis_config)
                self.redis_conn.ping()
                logger.info("Redis连接成功")
            except Exception as redis_error:
                logger.warning(f"Redis连接失败，将禁用缓存功能: {redis_error}")
                self.redis_conn = None

        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _get_cursor(self):
        """获取PostgreSQL游标"""
        if not self.pg_conn or self.pg_conn.closed:
            self._connect()
        return self.pg_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return self.hash_password(password) == password_hash
    
    # 用户管理
    def create_user(self, username: str, email: str, password: str, 
                   full_name: str = None, role: str = 'user') -> Dict:
        """创建用户"""
        try:
            cursor = self._get_cursor()
            password_hash = self.hash_password(password)
            
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name, role, email_verified)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id, username, email, full_name, role, is_premium, created_at
            """, (username, email, password_hash, full_name, role, True))
            
            user = dict(cursor.fetchone())
            cursor.close()
            
            # 缓存用户信息
            self._cache_user(user)
            
            logger.info(f"用户创建成功: {username}")
            return user
            
        except psycopg2.IntegrityError as e:
            if 'username' in str(e):
                raise ValueError("用户名已存在")
            elif 'email' in str(e):
                raise ValueError("邮箱已存在")
            else:
                raise ValueError("用户创建失败")
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            raise
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户"""
        try:
            # 先从缓存获取（如果Redis可用）
            if self.redis_conn:
                try:
                    cached_user = self.redis_conn.get(f"user:username:{username}")
                    if cached_user:
                        return json.loads(cached_user)
                except Exception as e:
                    logger.warning(f"Redis缓存读取失败: {e}")

            # 从数据库获取
            cursor = self._get_cursor()
            cursor.execute("""
                SELECT id, username, email, password_hash, full_name, role,
                       is_premium, premium_expires, created_at, last_login,
                       is_active, email_verified, two_factor_enabled
                FROM users WHERE username = %s AND is_active = TRUE
            """, (username,))

            user = cursor.fetchone()
            cursor.close()

            if user:
                user = dict(user)
                # 缓存用户信息
                self._cache_user(user)
                return user

            return None

        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """根据邮箱获取用户"""
        try:
            # 先从缓存获取（如果Redis可用）
            if self.redis_conn:
                try:
                    cached_user = self.redis_conn.get(f"user:email:{email}")
                    if cached_user:
                        return json.loads(cached_user)
                except Exception as e:
                    logger.warning(f"Redis缓存读取失败: {e}")

            # 从数据库获取
            cursor = self._get_cursor()
            cursor.execute("""
                SELECT id, username, email, password_hash, full_name, role,
                       is_premium, premium_expires, created_at, last_login,
                       is_active, email_verified, two_factor_enabled
                FROM users WHERE email = %s AND is_active = TRUE
            """, (email,))

            user = cursor.fetchone()
            cursor.close()

            if user:
                user = dict(user)
                # 缓存用户信息
                self._cache_user(user)
                return user

            return None

        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def authenticate_user(self, login_identifier: str, password: str) -> Optional[Dict]:
        """用户认证 - 支持用户名或邮箱登录"""
        try:
            # 首先尝试用户名登录
            user = self.get_user_by_username(login_identifier)
            
            # 如果用户名不存在，尝试邮箱登录
            if not user:
                user = self.get_user_by_email(login_identifier)
            
            if not user:
                return None
            
            # 验证密码
            if not self.verify_password(password, user['password_hash']):
                return None
            
            # 更新最后登录时间
            self.update_last_login(user['username'])
            
            # 移除密码哈希后返回
            user_info = {k: v for k, v in user.items() if k != 'password_hash'}
            return user_info
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return None
    
    def update_last_login(self, username: str):
        """更新最后登录时间"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                UPDATE users SET last_login = CURRENT_TIMESTAMP 
                WHERE username = %s
            """, (username,))
            cursor.close()
            
            # 清除缓存
            self._clear_user_cache(username)
            
        except Exception as e:
            logger.error(f"更新登录时间失败: {e}")
    
    def _cache_user(self, user: Dict):
        """缓存用户信息"""
        if not self.redis_conn:
            return
        try:
            user_json = json.dumps(user, default=str)
            # 缓存30分钟
            self.redis_conn.setex(f"user:username:{user['username']}", 1800, user_json)
            self.redis_conn.setex(f"user:email:{user['email']}", 1800, user_json)
            self.redis_conn.setex(f"user:id:{user['id']}", 1800, user_json)
        except Exception as e:
            logger.warning(f"缓存用户信息失败: {e}")

    def _clear_user_cache(self, username: str):
        """清除用户缓存"""
        if not self.redis_conn:
            return
        try:
            user = self.get_user_by_username(username)
            if user:
                self.redis_conn.delete(f"user:username:{username}")
                self.redis_conn.delete(f"user:email:{user['email']}")
                self.redis_conn.delete(f"user:id:{user['id']}")
        except Exception as e:
            logger.warning(f"清除用户缓存失败: {e}")
    
    # 策略管理
    def save_strategy(self, user_id: int, name: str, code: str, 
                     description: str = None, parameters: Dict = None) -> int:
        """保存策略"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                INSERT INTO strategies (user_id, name, description, code, parameters)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id
            """, (user_id, name, description, code, json.dumps(parameters) if parameters else None))
            
            strategy_id = cursor.fetchone()['id']
            cursor.close()
            
            logger.info(f"策略保存成功: {name} (ID: {strategy_id})")
            return strategy_id
            
        except Exception as e:
            logger.error(f"保存策略失败: {e}")
            raise
    
    def get_user_strategies(self, user_id: int) -> List[Dict]:
        """获取用户策略"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                SELECT id, name, description, parameters, is_public, is_premium,
                       category, created_at, updated_at, downloads, rating
                FROM strategies WHERE user_id = %s
                ORDER BY updated_at DESC
            """, (user_id,))
            
            strategies = [dict(row) for row in cursor.fetchall()]
            cursor.close()
            
            return strategies
            
        except Exception as e:
            logger.error(f"获取用户策略失败: {e}")
            return []
    
    # 回测记录管理
    def save_backtest_result(self, user_id: int, strategy_id: int, symbol: str,
                           start_date: str, end_date: str, initial_capital: float,
                           final_capital: float, total_return: float,
                           results: Dict, parameters: Dict = None) -> int:
        """保存回测结果"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                INSERT INTO backtest_results 
                (user_id, strategy_id, symbol, start_date, end_date, 
                 initial_capital, final_capital, total_return, results, parameters)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (user_id, strategy_id, symbol, start_date, end_date,
                  initial_capital, final_capital, total_return,
                  json.dumps(results), json.dumps(parameters) if parameters else None))
            
            result_id = cursor.fetchone()['id']
            cursor.close()
            
            logger.info(f"回测结果保存成功 (ID: {result_id})")
            return result_id
            
        except Exception as e:
            logger.error(f"保存回测结果失败: {e}")
            raise
    
    # 关注列表管理
    def add_to_watchlist(self, user_id: int, symbol: str, name: str = None,
                        asset_type: str = 'stock'):
        """添加到关注列表"""
        try:
            cursor = self._get_cursor()
            cursor.execute("""
                INSERT INTO watchlists (user_id, symbol, name, asset_type)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (user_id, symbol) DO NOTHING
            """, (user_id, symbol, name, asset_type))
            cursor.close()

            # 清除缓存
            if self.redis_conn:
                try:
                    self.redis_conn.delete(f"watchlist:{user_id}")
                except Exception as e:
                    logger.warning(f"清除关注列表缓存失败: {e}")

        except Exception as e:
            logger.error(f"添加关注失败: {e}")
            raise

    def get_watchlist(self, user_id: int) -> List[Dict]:
        """获取关注列表"""
        try:
            # 先从缓存获取（如果Redis可用）
            if self.redis_conn:
                try:
                    cached_list = self.redis_conn.get(f"watchlist:{user_id}")
                    if cached_list:
                        return json.loads(cached_list)
                except Exception as e:
                    logger.warning(f"Redis缓存读取失败: {e}")

            cursor = self._get_cursor()
            cursor.execute("""
                SELECT symbol, name, asset_type, added_at
                FROM watchlists WHERE user_id = %s
                ORDER BY added_at DESC
            """, (user_id,))

            watchlist = [dict(row) for row in cursor.fetchall()]
            cursor.close()

            # 缓存10分钟（如果Redis可用）
            if self.redis_conn:
                try:
                    self.redis_conn.setex(f"watchlist:{user_id}", 600,
                                        json.dumps(watchlist, default=str))
                except Exception as e:
                    logger.warning(f"缓存关注列表失败: {e}")

            return watchlist

        except Exception as e:
            logger.error(f"获取关注列表失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.pg_conn:
                self.pg_conn.close()
            if self.redis_conn:
                self.redis_conn.close()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")

# 全局数据库管理器实例
db_manager = None

def get_db_manager():
    """获取数据库管理器实例"""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager
