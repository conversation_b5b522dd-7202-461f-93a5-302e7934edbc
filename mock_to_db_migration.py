#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟数据到数据库迁移工具
将MOCK_USERS中的模拟数据迁移到PostgreSQL数据库
"""

import json
import logging
from datetime import datetime, timedelta
from user_manager import UserManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockToDbMigrator:
    """模拟数据到数据库迁移器"""
    
    def __init__(self):
        self.user_manager = UserManager()
        self.migration_log = []
        
    def log_migration(self, action, status, details=""):
        """记录迁移日志"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.migration_log.append(log_entry)
        logger.info(f"迁移日志: {action} - {status} - {details}")
    
    def get_mock_users_data(self):
        """获取模拟用户数据"""
        return {
            'admin': {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'full_name': '系统管理员',
                'role': 'admin',
                'is_premium': True,
                'premium_expires': (datetime.now() + timedelta(days=365)).isoformat(),
                'avatar_url': '/static/img/avatar-admin.png',
                'bio': '系统管理员账户，负责平台运营和用户管理',
                'phone': '+86-138-0000-0001',
                'region': 'cn',
                'risk_preference': 'conservative',
                'experience': 'expert',
                'total_strategies': 15,
                'total_backtests': 50,
                'total_profit': 125000.0,
                'win_rate': 78.5,
                'followers': 1250,
                'following': 25,
                'two_factor_enabled': False,
                'two_factor_secret': None,
                'backup_codes': []
            },
            'demo_user': {
                'username': 'demo_user',
                'email': '<EMAIL>',
                'password': 'demo123',
                'full_name': '演示用户',
                'role': 'user',
                'is_premium': False,
                'premium_expires': None,
                'avatar_url': '/static/img/avatar-demo.png',
                'bio': '这是一个演示账户，用于展示平台功能和特性',
                'phone': '+86-138-0000-0002',
                'region': 'cn',
                'risk_preference': 'moderate',
                'experience': 'intermediate',
                'total_strategies': 8,
                'total_backtests': 25,
                'total_profit': 15000.0,
                'win_rate': 65.2,
                'followers': 150,
                'following': 45,
                'two_factor_enabled': False,
                'two_factor_secret': None,
                'backup_codes': []
            },
            'vip_user': {
                'username': 'vip_user',
                'email': '<EMAIL>',
                'password': 'vip123',
                'full_name': 'VIP用户',
                'role': 'premium',
                'is_premium': True,
                'premium_expires': (datetime.now() + timedelta(days=30)).isoformat(),
                'avatar_url': '/static/img/avatar-vip.png',
                'bio': 'VIP会员，享受专业量化交易服务和高级功能',
                'phone': '+86-138-0000-0003',
                'region': 'cn',
                'risk_preference': 'aggressive',
                'experience': 'expert',
                'total_strategies': 25,
                'total_backtests': 80,
                'total_profit': 85000.0,
                'win_rate': 72.8,
                'followers': 850,
                'following': 120,
                'two_factor_enabled': True,
                'two_factor_secret': 'JBSWY3DPEHPK3PXP',
                'backup_codes': ['ABCD-1234', 'EFGH-5678', 'IJKL-9012']
            },
            'trader_pro': {
                'username': 'trader_pro',
                'email': '<EMAIL>',
                'password': 'trader123',
                'full_name': '专业交易员',
                'role': 'premium',
                'is_premium': True,
                'premium_expires': (datetime.now() + timedelta(days=365)).isoformat(),
                'avatar_url': '/static/img/avatar-trader.png',
                'bio': '专业量化交易员，专注于高频交易策略开发',
                'phone': '+86-138-0000-0004',
                'region': 'cn',
                'risk_preference': 'aggressive',
                'experience': 'expert',
                'total_strategies': 45,
                'total_backtests': 200,
                'total_profit': 350000.0,
                'win_rate': 82.3,
                'followers': 2500,
                'following': 80,
                'two_factor_enabled': True,
                'two_factor_secret': 'MFRGG2LTMVZWS3TH',
                'backup_codes': ['MNOP-3456', 'QRST-7890', 'UVWX-1234']
            },
            'newbie': {
                'username': 'newbie',
                'email': '<EMAIL>',
                'password': 'newbie123',
                'full_name': '新手用户',
                'role': 'user',
                'is_premium': False,
                'premium_expires': None,
                'avatar_url': '/static/img/avatar-newbie.png',
                'bio': '刚开始学习量化交易的新手，正在探索平台功能',
                'phone': '+86-138-0000-0005',
                'region': 'cn',
                'risk_preference': 'conservative',
                'experience': 'beginner',
                'total_strategies': 2,
                'total_backtests': 5,
                'total_profit': 500.0,
                'win_rate': 40.0,
                'followers': 10,
                'following': 200,
                'two_factor_enabled': False,
                'two_factor_secret': None,
                'backup_codes': []
            },
            'analyst': {
                'username': 'analyst',
                'email': '<EMAIL>',
                'password': 'analyst123',
                'full_name': '量化分析师',
                'role': 'premium',
                'is_premium': True,
                'premium_expires': (datetime.now() + timedelta(days=180)).isoformat(),
                'avatar_url': '/static/img/avatar-analyst.png',
                'bio': '专业量化分析师，擅长市场分析和策略优化',
                'phone': '+86-138-0000-0006',
                'region': 'cn',
                'risk_preference': 'moderate',
                'experience': 'expert',
                'total_strategies': 35,
                'total_backtests': 120,
                'total_profit': 180000.0,
                'win_rate': 75.6,
                'followers': 1800,
                'following': 150,
                'two_factor_enabled': True,
                'two_factor_secret': 'NBSWY3DPEHPK3PXQ',
                'backup_codes': ['YZAB-5678', 'CDEF-9012', 'GHIJ-3456']
            }
        }
    
    def get_mock_watchlists(self):
        """获取模拟关注列表数据"""
        return {
            'admin': [
                {'symbol': 'AAPL', 'type': 'stock', 'name': 'Apple Inc.'},
                {'symbol': 'MSFT', 'type': 'stock', 'name': 'Microsoft Corporation'},
                {'symbol': 'GOOGL', 'type': 'stock', 'name': 'Alphabet Inc.'},
                {'symbol': 'TSLA', 'type': 'stock', 'name': 'Tesla Inc.'},
                {'symbol': 'SPY', 'type': 'etf', 'name': 'SPDR S&P 500 ETF'}
            ],
            'demo_user': [
                {'symbol': 'AAPL', 'type': 'stock', 'name': 'Apple Inc.'},
                {'symbol': 'QQQ', 'type': 'etf', 'name': 'Invesco QQQ Trust'},
                {'symbol': 'NVDA', 'type': 'stock', 'name': 'NVIDIA Corporation'}
            ],
            'vip_user': [
                {'symbol': 'AAPL', 'type': 'stock', 'name': 'Apple Inc.'},
                {'symbol': 'MSFT', 'type': 'stock', 'name': 'Microsoft Corporation'},
                {'symbol': 'AMZN', 'type': 'stock', 'name': 'Amazon.com Inc.'},
                {'symbol': 'META', 'type': 'stock', 'name': 'Meta Platforms Inc.'},
                {'symbol': 'NFLX', 'type': 'stock', 'name': 'Netflix Inc.'},
                {'symbol': 'BTC-USD', 'type': 'crypto', 'name': 'Bitcoin'},
                {'symbol': 'ETH-USD', 'type': 'crypto', 'name': 'Ethereum'}
            ],
            'trader_pro': [
                {'symbol': 'SPY', 'type': 'etf', 'name': 'SPDR S&P 500 ETF'},
                {'symbol': 'QQQ', 'type': 'etf', 'name': 'Invesco QQQ Trust'},
                {'symbol': 'IWM', 'type': 'etf', 'name': 'iShares Russell 2000 ETF'},
                {'symbol': 'VIX', 'type': 'index', 'name': 'CBOE Volatility Index'},
                {'symbol': 'GLD', 'type': 'etf', 'name': 'SPDR Gold Shares'},
                {'symbol': 'TLT', 'type': 'etf', 'name': 'iShares 20+ Year Treasury Bond ETF'}
            ],
            'newbie': [
                {'symbol': 'AAPL', 'type': 'stock', 'name': 'Apple Inc.'},
                {'symbol': 'SPY', 'type': 'etf', 'name': 'SPDR S&P 500 ETF'}
            ],
            'analyst': [
                {'symbol': 'AAPL', 'type': 'stock', 'name': 'Apple Inc.'},
                {'symbol': 'MSFT', 'type': 'stock', 'name': 'Microsoft Corporation'},
                {'symbol': 'GOOGL', 'type': 'stock', 'name': 'Alphabet Inc.'},
                {'symbol': 'AMZN', 'type': 'stock', 'name': 'Amazon.com Inc.'},
                {'symbol': 'TSLA', 'type': 'stock', 'name': 'Tesla Inc.'},
                {'symbol': 'SPY', 'type': 'etf', 'name': 'SPDR S&P 500 ETF'},
                {'symbol': 'QQQ', 'type': 'etf', 'name': 'Invesco QQQ Trust'}
            ]
        }
    
    def migrate_users(self):
        """迁移用户数据"""
        logger.info("开始迁移用户数据...")
        
        mock_users = self.get_mock_users_data()
        success_count = 0
        error_count = 0
        
        for username, user_data in mock_users.items():
            try:
                # 检查用户是否已存在
                existing_user = self.user_manager.get_user_by_username(username)
                if existing_user:
                    self.log_migration(f"用户迁移", "跳过", f"用户 {username} 已存在")
                    continue
                
                # 创建用户
                success, message = self.user_manager.create_user(
                    username=user_data['username'],
                    email=user_data['email'],
                    password=user_data['password'],
                    full_name=user_data['full_name'],
                    role=user_data['role'],
                    avatar_url=user_data.get('avatar_url', '/static/img/avatar-default.png'),
                    bio=user_data.get('bio', ''),
                    phone=user_data.get('phone', ''),
                    region=user_data.get('region', 'cn'),
                    risk_preference=user_data.get('risk_preference', 'moderate'),
                    experience=user_data.get('experience', 'beginner')
                )
                
                if success:
                    # 更新用户统计数据
                    stats_success, _ = self.user_manager.update_user_stats(
                        username,
                        total_strategies=user_data.get('total_strategies', 0),
                        total_backtests=user_data.get('total_backtests', 0),
                        total_profit=user_data.get('total_profit', 0.0),
                        win_rate=user_data.get('win_rate', 0.0),
                        followers=user_data.get('followers', 0),
                        following=user_data.get('following', 0)
                    )
                    
                    # 设置VIP状态
                    if user_data.get('is_premium'):
                        if user_data.get('premium_expires'):
                            # 计算剩余天数
                            expires_date = datetime.fromisoformat(user_data['premium_expires'])
                            days_left = (expires_date - datetime.now()).days
                            if days_left > 0:
                                if days_left <= 35:
                                    plan = 'monthly'
                                elif days_left <= 400:
                                    plan = 'yearly'
                                else:
                                    plan = 'lifetime'
                                self.user_manager.upgrade_to_premium(username, plan)
                    
                    # 设置2FA（如果启用）
                    if user_data.get('two_factor_enabled'):
                        self.user_manager.update_2fa_settings(
                            username,
                            True,
                            user_data.get('two_factor_secret'),
                            user_data.get('backup_codes', [])
                        )
                    
                    success_count += 1
                    self.log_migration(f"用户迁移", "成功", f"用户 {username} 迁移成功")
                else:
                    error_count += 1
                    self.log_migration(f"用户迁移", "失败", f"用户 {username} 迁移失败: {message}")
                    
            except Exception as e:
                error_count += 1
                self.log_migration(f"用户迁移", "错误", f"用户 {username} 迁移出错: {str(e)}")
        
        logger.info(f"用户数据迁移完成: 成功 {success_count} 个，失败 {error_count} 个")
        return success_count, error_count

    def migrate_watchlists(self):
        """迁移关注列表数据"""
        logger.info("开始迁移关注列表数据...")

        mock_watchlists = self.get_mock_watchlists()
        success_count = 0
        error_count = 0

        for username, watchlist in mock_watchlists.items():
            try:
                # 检查用户是否存在
                user = self.user_manager.get_user_by_username(username)
                if not user:
                    self.log_migration(f"关注列表迁移", "跳过", f"用户 {username} 不存在")
                    continue

                for item in watchlist:
                    try:
                        success, message = self.user_manager.add_to_watchlist(
                            username,
                            item['symbol'],
                            item['type'],
                            item['name']
                        )

                        if success:
                            success_count += 1
                            self.log_migration(f"关注列表迁移", "成功", f"用户 {username} 添加关注 {item['symbol']}")
                        else:
                            if "已在关注列表中" in message:
                                self.log_migration(f"关注列表迁移", "跳过", f"用户 {username} 的 {item['symbol']} 已存在")
                            else:
                                error_count += 1
                                self.log_migration(f"关注列表迁移", "失败", f"用户 {username} 添加关注 {item['symbol']} 失败: {message}")
                    except Exception as e:
                        error_count += 1
                        self.log_migration(f"关注列表迁移", "错误", f"用户 {username} 添加关注 {item['symbol']} 出错: {str(e)}")

            except Exception as e:
                error_count += 1
                self.log_migration(f"关注列表迁移", "错误", f"用户 {username} 关注列表迁移出错: {str(e)}")

        logger.info(f"关注列表数据迁移完成: 成功 {success_count} 个，失败 {error_count} 个")
        return success_count, error_count

    def run_full_migration(self):
        """运行完整迁移"""
        logger.info("🚀 开始完整数据迁移...")

        total_success = 0
        total_error = 0

        # 迁移用户数据
        user_success, user_error = self.migrate_users()
        total_success += user_success
        total_error += user_error

        # 迁移关注列表
        watchlist_success, watchlist_error = self.migrate_watchlists()
        total_success += watchlist_success
        total_error += watchlist_error

        # 生成迁移报告
        self.generate_migration_report(total_success, total_error)

        logger.info(f"🎉 完整数据迁移完成: 总成功 {total_success} 个，总失败 {total_error} 个")
        return total_success, total_error

    def generate_migration_report(self, total_success, total_error):
        """生成迁移报告"""
        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"""# QuantTradeX 模拟数据迁移报告

## 迁移概览
- **迁移时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **迁移类型**: 模拟数据到PostgreSQL数据库
- **总成功数**: {total_success}
- **总失败数**: {total_error}
- **成功率**: {(total_success / (total_success + total_error) * 100):.1f}% (如果有数据)

## 迁移详情

### 用户数据迁移
- 迁移了6个模拟用户账户
- 包含完整的用户资料、统计数据、VIP状态和2FA设置
- 用户角色：管理员、普通用户、VIP用户、专业交易员等

### 关注列表迁移
- 迁移了所有用户的关注列表
- 包含股票、ETF、加密货币等多种资产类型
- 保持了用户的个性化关注偏好

## 迁移日志
""")

            # 写入详细日志
            for log_entry in self.migration_log:
                f.write(f"- **{log_entry['timestamp']}**: {log_entry['action']} - {log_entry['status']} - {log_entry['details']}\n")

            f.write(f"""
## 验证建议

### 1. 用户登录测试
```bash
# 测试管理员登录
curl -X POST http://localhost:5000/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{{"username":"admin","password":"admin123"}}'

# 测试演示用户登录
curl -X POST http://localhost:5000/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{{"username":"demo_user","password":"demo123"}}'
```

### 2. 数据完整性检查
- 检查用户资料页面是否正常显示
- 验证VIP用户的会员状态
- 测试2FA功能（vip_user, trader_pro, analyst）
- 检查关注列表是否正确显示

### 3. 功能测试
- 测试用户注册和登录
- 验证个人资料更新功能
- 检查关注列表添加/删除功能
- 测试VIP升级流程

## 注意事项
- 所有密码都是明文存储在迁移脚本中，仅用于演示
- 2FA密钥是预设的测试密钥，生产环境应重新生成
- 建议在生产环境中修改默认密码
- 定期备份数据库以防数据丢失

## 下一步
1. 运行验证测试确保迁移成功
2. 开始开发性能优化功能
3. 实施缓存策略
4. 开发实盘交易系统
5. 构建高级报表系统
""")

        logger.info(f"迁移报告已生成: {report_file}")
        return report_file

    def verify_migration(self):
        """验证迁移结果"""
        logger.info("开始验证迁移结果...")

        verification_results = {
            'users_migrated': 0,
            'users_with_premium': 0,
            'users_with_2fa': 0,
            'total_watchlist_items': 0,
            'verification_passed': True,
            'issues': []
        }

        try:
            # 验证用户数据
            mock_users = self.get_mock_users_data()
            for username in mock_users.keys():
                user = self.user_manager.get_user_by_username(username)
                if user:
                    verification_results['users_migrated'] += 1

                    if user.get('is_premium'):
                        verification_results['users_with_premium'] += 1

                    if user.get('two_factor_enabled'):
                        verification_results['users_with_2fa'] += 1

                    # 验证关注列表
                    watchlist = self.user_manager.get_user_watchlist(username)
                    verification_results['total_watchlist_items'] += len(watchlist)
                else:
                    verification_results['issues'].append(f"用户 {username} 未找到")
                    verification_results['verification_passed'] = False

            logger.info(f"验证完成: {verification_results}")
            return verification_results

        except Exception as e:
            logger.error(f"验证过程出错: {e}")
            verification_results['verification_passed'] = False
            verification_results['issues'].append(f"验证过程出错: {str(e)}")
            return verification_results

def main():
    """主函数"""
    print("🚀 QuantTradeX 模拟数据迁移工具")
    print("=" * 50)

    migrator = MockToDbMigrator()

    try:
        # 运行完整迁移
        total_success, total_error = migrator.run_full_migration()

        # 验证迁移结果
        verification = migrator.verify_migration()

        print("\n" + "=" * 50)
        print("🎉 迁移完成!")
        print(f"✅ 成功: {total_success} 项")
        print(f"❌ 失败: {total_error} 项")
        print(f"👥 用户迁移: {verification['users_migrated']} 个")
        print(f"💎 VIP用户: {verification['users_with_premium']} 个")
        print(f"🔐 2FA用户: {verification['users_with_2fa']} 个")
        print(f"⭐ 关注项目: {verification['total_watchlist_items']} 个")

        if verification['verification_passed']:
            print("✅ 验证通过!")
        else:
            print("❌ 验证失败:")
            for issue in verification['issues']:
                print(f"  - {issue}")

        print("\n📋 查看详细报告: migration_report_*.md")

    except Exception as e:
        logger.error(f"迁移过程出错: {e}")
        print(f"❌ 迁移失败: {e}")

if __name__ == '__main__':
    main()
