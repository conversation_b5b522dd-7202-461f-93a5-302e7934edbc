#!/usr/bin/env python3
"""
数据库初始化脚本
创建PostgreSQL数据库、用户和表结构
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import hashlib
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'postgres',
    'password': '',  # 默认无密码
    'database': 'postgres'  # 连接到默认数据库
}

TARGET_DB = 'quanttradex'
TARGET_USER = 'quanttradex_user'
TARGET_PASSWORD = 'quanttradex_2024!'

def create_database_and_user():
    """创建数据库和用户"""
    try:
        # 连接到PostgreSQL
        conn = psycopg2.connect(**DB_CONFIG)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (TARGET_DB,))
        if not cursor.fetchone():
            print(f"创建数据库: {TARGET_DB}")
            cursor.execute(f"CREATE DATABASE {TARGET_DB}")
        else:
            print(f"数据库 {TARGET_DB} 已存在")
        
        # 检查用户是否存在
        cursor.execute("SELECT 1 FROM pg_user WHERE usename = %s", (TARGET_USER,))
        if not cursor.fetchone():
            print(f"创建用户: {TARGET_USER}")
            cursor.execute(f"CREATE USER {TARGET_USER} WITH PASSWORD '{TARGET_PASSWORD}'")
        else:
            print(f"用户 {TARGET_USER} 已存在")
        
        # 授权
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {TARGET_DB} TO {TARGET_USER}")
        print(f"已授权用户 {TARGET_USER} 访问数据库 {TARGET_DB}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"创建数据库和用户失败: {e}")
        return False

def create_tables():
    """创建表结构"""
    try:
        # 连接到目标数据库
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=TARGET_USER,
            password=TARGET_PASSWORD,
            database=TARGET_DB
        )
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                role VARCHAR(20) DEFAULT 'user',
                is_premium BOOLEAN DEFAULT FALSE,
                premium_expires TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                email_verified BOOLEAN DEFAULT FALSE,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret VARCHAR(32)
            )
        """)
        print("创建用户表完成")
        
        # 创建策略表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategies (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                code TEXT NOT NULL,
                parameters JSONB,
                is_public BOOLEAN DEFAULT FALSE,
                is_premium BOOLEAN DEFAULT FALSE,
                category VARCHAR(50),
                tags TEXT[],
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                downloads INTEGER DEFAULT 0,
                rating DECIMAL(3,2) DEFAULT 0.0,
                rating_count INTEGER DEFAULT 0
            )
        """)
        print("创建策略表完成")
        
        # 创建回测记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backtest_results (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                strategy_id INTEGER REFERENCES strategies(id) ON DELETE CASCADE,
                symbol VARCHAR(20) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                initial_capital DECIMAL(15,2) NOT NULL,
                final_capital DECIMAL(15,2) NOT NULL,
                total_return DECIMAL(10,4) NOT NULL,
                sharpe_ratio DECIMAL(10,4),
                max_drawdown DECIMAL(10,4),
                win_rate DECIMAL(5,2),
                total_trades INTEGER,
                parameters JSONB,
                results JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("创建回测记录表完成")
        
        # 创建论坛帖子表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS forum_posts (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(200) NOT NULL,
                content TEXT NOT NULL,
                category VARCHAR(50),
                tags TEXT[],
                views INTEGER DEFAULT 0,
                likes INTEGER DEFAULT 0,
                replies INTEGER DEFAULT 0,
                is_pinned BOOLEAN DEFAULT FALSE,
                is_locked BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("创建论坛帖子表完成")
        
        # 创建关注列表表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS watchlists (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                symbol VARCHAR(20) NOT NULL,
                name VARCHAR(100),
                asset_type VARCHAR(20) DEFAULT 'stock',
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, symbol)
            )
        """)
        print("创建关注列表表完成")
        
        # 创建交易记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trades (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                strategy_id INTEGER REFERENCES strategies(id) ON DELETE SET NULL,
                symbol VARCHAR(20) NOT NULL,
                action VARCHAR(10) NOT NULL,
                quantity INTEGER NOT NULL,
                price DECIMAL(15,4) NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_simulated BOOLEAN DEFAULT TRUE
            )
        """)
        print("创建交易记录表完成")
        
        # 创建支付订单表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payment_orders (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                order_id VARCHAR(100) UNIQUE NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'CNY',
                payment_method VARCHAR(20) NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                plan_type VARCHAR(20) NOT NULL,
                plan_duration INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                paid_at TIMESTAMP,
                expires_at TIMESTAMP
            )
        """)
        print("创建支付订单表完成")
        
        # 提交事务
        conn.commit()
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"创建表结构失败: {e}")
        return False

def create_default_users():
    """创建默认用户"""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=TARGET_USER,
            password=TARGET_PASSWORD,
            database=TARGET_DB
        )
        cursor = conn.cursor()
        
        # 默认用户数据
        default_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'full_name': '系统管理员',
                'role': 'admin',
                'is_premium': True
            },
            {
                'username': 'trader1',
                'email': '<EMAIL>',
                'password': 'password123',
                'full_name': '交易员一号',
                'role': 'user',
                'is_premium': False
            },
            {
                'username': 'vip_user',
                'email': '<EMAIL>',
                'password': 'password123',
                'full_name': 'VIP用户',
                'role': 'user',
                'is_premium': True
            }
        ]
        
        for user_data in default_users:
            # 检查用户是否已存在
            cursor.execute("SELECT id FROM users WHERE username = %s OR email = %s", 
                         (user_data['username'], user_data['email']))
            if cursor.fetchone():
                print(f"用户 {user_data['username']} 已存在，跳过")
                continue
            
            # 创建密码哈希
            password_hash = hashlib.sha256(user_data['password'].encode()).hexdigest()
            
            # 插入用户
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name, role, is_premium, email_verified)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                user_data['username'],
                user_data['email'],
                password_hash,
                user_data['full_name'],
                user_data['role'],
                user_data['is_premium'],
                True
            ))
            print(f"创建用户: {user_data['username']}")
        
        conn.commit()
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"创建默认用户失败: {e}")
        return False

def main():
    """主函数"""
    print("开始初始化数据库...")
    
    # 1. 创建数据库和用户
    if not create_database_and_user():
        print("数据库和用户创建失败，退出")
        sys.exit(1)
    
    # 2. 创建表结构
    if not create_tables():
        print("表结构创建失败，退出")
        sys.exit(1)
    
    # 3. 创建默认用户
    if not create_default_users():
        print("默认用户创建失败，退出")
        sys.exit(1)
    
    print("\n数据库初始化完成！")
    print(f"数据库: {TARGET_DB}")
    print(f"用户: {TARGET_USER}")
    print(f"密码: {TARGET_PASSWORD}")
    print("\n默认用户账户:")
    print("- admin / <EMAIL> / admin123 (管理员)")
    print("- trader1 / <EMAIL> / password123 (普通用户)")
    print("- vip_user / <EMAIL> / password123 (VIP用户)")

if __name__ == "__main__":
    main()
