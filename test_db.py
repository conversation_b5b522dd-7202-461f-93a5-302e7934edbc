#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

import psycopg2
import psycopg2.extras
import hashlib
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_direct_connection():
    """直接测试数据库连接"""
    try:
        # 数据库配置
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'quanttradex_user',
            'password': 'quanttradex_2024!',
            'database': 'quanttradex'
        }
        
        print("=== 测试PostgreSQL连接 ===")
        conn = psycopg2.connect(**config)
        conn.autocommit = True
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print("✅ PostgreSQL连接成功")
        
        # 测试查询用户
        print("\n=== 测试用户查询 ===")
        cursor.execute("SELECT username, email, password_hash FROM users WHERE email = %s", 
                      ('<EMAIL>',))
        user = cursor.fetchone()
        
        if user:
            user = dict(user)
            print(f"✅ 找到用户: {user['username']} ({user['email']})")
            
            # 测试密码验证
            print("\n=== 测试密码验证 ===")
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            print(f"输入密码哈希: {password_hash}")
            print(f"数据库密码哈希: {user['password_hash']}")
            
            if password_hash == user['password_hash']:
                print("✅ 密码验证成功")
            else:
                print("❌ 密码验证失败")
        else:
            print("❌ 未找到用户")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

def test_database_manager():
    """测试数据库管理器"""
    try:
        print("\n=== 测试数据库管理器 ===")
        from database_manager import DatabaseManager
        
        # 创建管理器实例（不使用Redis）
        db = DatabaseManager()
        
        print("✅ 数据库管理器创建成功")
        
        # 测试邮箱查询
        print("\n=== 测试邮箱查询 ===")
        user = db.get_user_by_email('<EMAIL>')
        
        if user:
            print(f"✅ 邮箱查询成功: {user['username']}")
            
            # 测试密码验证
            print("\n=== 测试密码验证 ===")
            result = db.verify_password('admin123', user['password_hash'])
            print(f"密码验证结果: {result}")
            
            # 测试完整认证
            print("\n=== 测试完整认证 ===")
            auth_result = db.authenticate_user('<EMAIL>', 'admin123')
            if auth_result:
                print(f"✅ 邮箱认证成功: {auth_result['username']}")
            else:
                print("❌ 邮箱认证失败")
                
            # 测试用户名认证
            print("\n=== 测试用户名认证 ===")
            auth_result2 = db.authenticate_user('admin', 'admin123')
            if auth_result2:
                print(f"✅ 用户名认证成功: {auth_result2['username']}")
            else:
                print("❌ 用户名认证失败")
        else:
            print("❌ 邮箱查询失败")
            
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_connection()
    test_database_manager()
